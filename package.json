{"name": "ui-experiments", "version": "0.0.1", "private": true, "scripts": {"build": "turbo build", "dev": "turbo dev", "lint": "turbo lint", "format": "prettier --config .prettierrc.js --write \"**/*.{ts,tsx,md}\""}, "devDependencies": {"@workspace/eslint-config": "workspace:*", "@workspace/typescript-config": "workspace:*", "prettier": "^3.6.2", "turbo": "^2.4.2", "typescript": "5.8.2"}, "packageManager": "pnpm@9.12.3", "engines": {"node": ">=20"}}