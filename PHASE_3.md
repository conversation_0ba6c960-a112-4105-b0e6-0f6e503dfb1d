# Phase 3: Session Scheduling with <PERSON>ag & Drop

## 3.1 Session Data Model Evolution

- Transform CalendarEvent to TrainingSession model
- Add participant tracking (3-5 customers per session)
- Implement 1-hour session duration constraints

## 3.2 Enhanced Drag & Drop

- Create customer list sidebar for dragging
- Implement drag customers into time slots
- Add visual indicators for session capacity (3-5 participants)
- Prevent scheduling customers with 0 credits

## 3.3 Session Management Interface

- Build session details modal with participant list
- Add remove customer from session functionality
- Implement session rescheduling via drag & drop
- Add conflict detection for double-booking

---

### Tasks

#### Session Model Evolution

- Transform event model to training session
- Add participant tracking schema
- Implement session constraints validation
- Create session-customer relationship management

#### Enhanced Drag & Drop

- Build draggable customer list sidebar
- Implement drop zones in calendar time slots
- Add visual feedback for session capacity
- Create session participant management interface

#### Session Management

- Build session details modal
- Add participant add/remove functionality
- Implement session rescheduling
- Add conflict detection logic
