'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import {
  RiCheckLine,
  RiCloseLine,
  RiTimeLine,
  RiGroupLine,
  RiCoinLine,
  RiAlertLine,
  RiRefreshLine,
} from '@remixicon/react';
import { useWorkoutsList, updateWorkout } from '@/hooks/use-workouts';

import { notify } from '@/lib/notification-service';
import type { WorkoutResponse } from '@/lib/validations';
import { useCreditTransactions } from '@/hooks/use-credit-transactions';

interface SessionManagementDashboardProps {
  className?: string;
}

export function SessionManagementDashboard({ className }: SessionManagementDashboardProps) {
  const [updatingWorkout, setUpdatingWorkout] = useState<string | null>(null);

  const {
    workouts,
    loading: workoutsLoading,
    mutate: refreshWorkouts,
  } = useWorkoutsList({
    status: 'scheduled', // Focus on scheduled sessions that need approval
    sortBy: 'startTime',
    sortOrder: 'asc',
  });

  const {
    data: transactionsData,
    isLoading: transactionsLoading,
    mutate: refreshTransactions,
  } = useCreditTransactions({
    limit: 10,
    offset: 0,
    sortBy: 'createdAt',
    sortOrder: 'desc',
  });

  const transactions = transactionsData?.data || [];

  const handleStatusChange = async (workoutId: string, newStatus: 'confirmed' | 'cancelled') => {
    setUpdatingWorkout(workoutId);
    try {
      // Find the workout to get details for notification
      const workout = workouts.find((w) => w.id === workoutId);
      const participantCount = workout?.participantCount || 0;

      await updateWorkout(workoutId, { status: newStatus });
      await refreshWorkouts();

      // Show appropriate notification
      if (workout) {
        if (newStatus === 'confirmed') {
          notify.sessionConfirmed(workout.title, participantCount);
        } else if (newStatus === 'cancelled') {
          notify.sessionCancelled(workout.title, participantCount);
        }
      }
    } catch (error) {
      console.error('Failed to update session status:', error);
      notify.operationFailed(`${newStatus.toLowerCase()} session`, error instanceof Error ? error.message : undefined);
    } finally {
      setUpdatingWorkout(null);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'bg-yellow-100 text-yellow-800';
      case 'confirmed':
        return 'bg-green-100 text-green-800';
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTransactionTypeColor = (type: string) => {
    switch (type) {
      case 'purchase':
        return 'bg-green-100 text-green-800';
      case 'deduction':
        return 'bg-red-100 text-red-800';
      case 'refund':
        return 'bg-blue-100 text-blue-800';
      case 'adjustment':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDateTime = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(new Date(date));
  };

  const getSessionWarnings = (workout: WorkoutResponse) => {
    const warnings: string[] = [];
    const participantCount = workout.participantCount || 0;

    if (participantCount < workout.minParticipants) {
      const needed = workout.minParticipants - participantCount;
      warnings.push(`Need ${needed} more participant${needed > 1 ? 's' : ''} to meet minimum`);
    }

    if (participantCount === workout.maxParticipants) {
      warnings.push('At maximum capacity');
    }

    // Check if session is soon (within 2 hours)
    const now = new Date();
    const sessionTime = new Date(workout.startTime);
    const hoursUntil = (sessionTime.getTime() - now.getTime()) / (1000 * 60 * 60);

    if (hoursUntil < 2 && hoursUntil > 0) {
      warnings.push('Session starts soon');
    }

    return warnings;
  };

  return (
    <div className={className}>
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold">Session Management</h2>
          <p className="text-muted-foreground">Manage workout sessions, approvals, and credit transactions</p>
        </div>
        <Button
          variant="outline"
          onClick={() => {
            refreshWorkouts();
            refreshTransactions();
            notify.dataRefreshed();
          }}
          disabled={workoutsLoading || transactionsLoading}
        >
          <RiRefreshLine className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      <Tabs defaultValue="approval-queue" className="space-y-6">
        <TabsList>
          <TabsTrigger value="approval-queue">
            <RiTimeLine className="h-4 w-4 mr-2" />
            Approval Queue
          </TabsTrigger>
          <TabsTrigger value="credit-transactions">
            <RiCoinLine className="h-4 w-4 mr-2" />
            Credit Transactions
          </TabsTrigger>
        </TabsList>

        <TabsContent value="approval-queue" className="space-y-4">
          <Card className="border-0 shadow-none bg-transparent">
            <CardHeader className="px-0">
              <CardTitle>Sessions Pending Approval</CardTitle>
              <CardDescription>Review and approve scheduled workout sessions</CardDescription>
            </CardHeader>
            <CardContent className="px-0">
              {workoutsLoading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b border-primary mx-auto"></div>
                  <p className="text-muted-foreground mt-2">Loading sessions...</p>
                </div>
              ) : workouts.length === 0 ? (
                <div className="text-center py-8">
                  <RiCheckLine className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No sessions pending approval</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {workouts.map((workout) => {
                    const warnings = getSessionWarnings(workout);
                    const participantCount = workout.participantCount || 0;
                    const canConfirm = participantCount >= workout.minParticipants;

                    return (
                      <Card key={workout.id} className="border rounded-lg p-4 space-y-3">
                        <div className="flex items-start justify-between">
                          <div className="space-y-1">
                            <h3 className="font-semibold">{workout.title}</h3>
                            <p className="text-sm text-muted-foreground">{formatDateTime(workout.startTime)}</p>
                            {workout.description && (
                              <p className="text-sm text-muted-foreground">{workout.description}</p>
                            )}
                          </div>
                          <Badge className={getStatusColor(workout.status)}>{workout.status}</Badge>
                        </div>

                        <div className="flex items-center gap-4 text-sm">
                          <div className="flex items-center gap-1">
                            <RiGroupLine className="h-4 w-4" />
                            <span>
                              {participantCount}/{workout.maxParticipants} participants
                            </span>
                          </div>
                          <div className="text-muted-foreground">Min: {workout.minParticipants}</div>
                        </div>

                        {warnings.length > 0 && (
                          <div className="space-y-1">
                            {warnings.map((warning, index) => (
                              <div key={index} className="flex items-center gap-2 text-sm text-amber-600">
                                <RiAlertLine className="h-4 w-4" />
                                {warning}
                              </div>
                            ))}
                          </div>
                        )}

                        <div className="flex items-center gap-2 pt-2">
                          <Button
                            size="sm"
                            onClick={() => handleStatusChange(workout.id, 'confirmed')}
                            disabled={!canConfirm || updatingWorkout === workout.id}
                            className="text-green-600 hover:text-green-700"
                            variant="outline"
                          >
                            {updatingWorkout === workout.id ? (
                              <div className="animate-spin rounded-full h-3 w-3 border-b border-current mr-1"></div>
                            ) : (
                              <RiCheckLine className="h-3 w-3 mr-1" />
                            )}
                            Confirm
                          </Button>

                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleStatusChange(workout.id, 'cancelled')}
                            disabled={updatingWorkout === workout.id}
                            className="text-red-600 hover:text-red-700"
                          >
                            {updatingWorkout === workout.id ? (
                              <div className="animate-spin rounded-full h-3 w-3 border-b border-current mr-1"></div>
                            ) : (
                              <RiCloseLine className="h-3 w-3 mr-1" />
                            )}
                            Cancel
                          </Button>

                          {!canConfirm && (
                            <span className="text-xs text-muted-foreground ml-2">
                              Cannot confirm: minimum participants not met
                            </span>
                          )}
                        </div>
                      </Card>
                    );
                  })}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="credit-transactions" className="space-y-4">
          <Card className="border-0 shadow-none bg-transparent">
            <CardHeader className="px-0">
              <CardTitle>Recent Credit Transactions</CardTitle>
              <CardDescription>Monitor credit purchases, deductions, and refunds</CardDescription>
            </CardHeader>
            <CardContent className="px-0">
              {transactionsLoading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b border-primary mx-auto"></div>
                  <p className="text-muted-foreground mt-2">Loading transactions...</p>
                </div>
              ) : transactions.length === 0 ? (
                <div className="text-center py-8">
                  <RiCoinLine className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No recent transactions</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {transactions.map((transaction) => (
                    <div key={transaction.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <Badge className={getTransactionTypeColor(transaction.type)}>{transaction.type}</Badge>
                          <span className="font-medium">{transaction.customerName}</span>
                        </div>
                        <p className="text-sm text-muted-foreground">{transaction.description}</p>
                        <p className="text-xs text-muted-foreground">{formatDateTime(transaction.createdAt)}</p>
                      </div>
                      <div className="text-right">
                        <div className={`font-semibold ${transaction.amount > 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {transaction.amount > 0 ? '+' : ''}
                          {transaction.amount}
                        </div>
                        <div className="text-xs text-muted-foreground">Balance: {transaction.balanceAfter}</div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
