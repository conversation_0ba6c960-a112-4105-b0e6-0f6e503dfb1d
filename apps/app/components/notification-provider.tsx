'use client';

import { createContext, useContext, useEffect, useState } from 'react';
import { Toaster } from 'sonner';
import { notify } from '@/lib/notification-service';

interface NotificationContextType {
  showNotification: typeof notify.show;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export function useNotifications() {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
}

interface NotificationProviderProps {
  children: React.ReactNode;
}

export function NotificationProvider({ children }: NotificationProviderProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const contextValue: NotificationContextType = {
    showNotification: notify.show,
  };

  if (!mounted) {
    return <>{children}</>;
  }

  return (
    <NotificationContext.Provider value={contextValue}>
      {children}
      <Toaster
        position="top-right"
        expand={true}
        richColors={true}
        closeButton={true}
        toastOptions={{
          duration: 5000,
          style: {
            background: 'hsl(var(--background))',
            border: '1px solid hsl(var(--border))',
            color: 'hsl(var(--foreground))',
          },
        }}
      />
    </NotificationContext.Provider>
  );
}
