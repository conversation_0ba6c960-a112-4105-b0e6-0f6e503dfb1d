export type CalendarView = 'month' | 'week' | 'day' | 'agenda';

export interface CalendarEvent {
  id: string;
  title: string;
  description?: string;
  start: Date;
  end: Date;
  allDay?: boolean;
  color?: EventColor;
  label?: string;
  location?: string;
  // Workout-specific fields
  workoutId?: string;
  trainerId?: string;
  minParticipants?: number;
  maxParticipants?: number;
  participantCount?: number;
  status?: 'scheduled' | 'confirmed' | 'completed' | 'cancelled';
  participants?: WorkoutParticipant[];
}

export interface WorkoutParticipant {
  id: string;
  customerId: string;
  customerName: string;
  status: 'enrolled' | 'confirmed' | 'cancelled';
  enrolledAt: Date;
  confirmedAt?: Date | null;
  creditDeducted: boolean;
}

export type EventColor = 'blue' | 'orange' | 'violet' | 'rose' | 'emerald';

// Utility function to convert WorkoutResponse to CalendarEvent
export function workoutToCalendarEvent(workout: any): CalendarEvent {
  return {
    id: workout.id,
    workoutId: workout.id,
    trainerId: workout.trainerId,
    title: workout.title,
    description: workout.description || undefined,
    start: new Date(workout.startTime),
    end: new Date(workout.endTime),
    location: workout.location || undefined,
    minParticipants: workout.minParticipants,
    maxParticipants: workout.maxParticipants,
    participantCount: workout.participantCount || workout.participants?.length || 0,
    status: workout.status,
    participants:
      workout.participants?.map((p: any) => ({
        id: p.id,
        customerId: p.customerId,
        customerName: p.customerName,
        status: p.status,
        enrolledAt: new Date(p.enrolledAt),
        confirmedAt: p.confirmedAt ? new Date(p.confirmedAt) : null,
        creditDeducted: p.creditDeducted,
      })) || [],
    // Set color based on workout status
    color: getWorkoutColor(
      workout.status,
      workout.participantCount || 0,
      workout.minParticipants || 3,
      workout.maxParticipants || 5
    ),
  };
}

// Utility function to determine workout color based on status and capacity
export function getWorkoutColor(
  status: string,
  participantCount: number,
  minParticipants: number,
  maxParticipants: number
): EventColor {
  switch (status) {
    case 'cancelled':
      return 'rose';
    case 'completed':
      return 'emerald';
    case 'confirmed':
      return 'blue';
    case 'scheduled':
    default:
      // Color based on capacity for scheduled workouts
      if (participantCount >= maxParticipants) {
        return 'violet'; // Full capacity
      } else if (participantCount >= minParticipants) {
        return 'orange'; // Minimum met
      } else {
        return 'blue'; // Below minimum
      }
  }
}
