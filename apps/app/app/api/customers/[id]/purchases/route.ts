import { NextRequest } from 'next/server';
import { db, customers, customerPurchases } from '@workspace/auth/server';
import { eq, and, desc, asc, count } from 'drizzle-orm';
import {
  createPurchaseSchema,
  purchaseQuerySchema,
  type PaginatedResponse,
  type PurchaseResponse,
} from '@/lib/validations';
import {
  withAuth,
  validateRequestBody,
  validateQueryParams,
  createSuccessResponse,
  createErrorResponse,
  handleApiError,
  getTrainerIdFromUser,
} from '@/lib/api-utils';
import { addCreditsFromPurchase } from '@/lib/credit-service';

// GET /api/customers/[id]/purchases - Get customer purchase history
export const GET = withAuth(async (request: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  try {
    const { id: customerId } = await params;
    const trainerId = await getTrainerIdFromUser(user);

    if (!trainerId) {
      return createErrorResponse('Forbidden', 'Invalid trainer access', 403);
    }

    // Verify customer belongs to trainer
    const [customer] = await db
      .select()
      .from(customers)
      .where(and(eq(customers.id, customerId), eq(customers.trainerId, trainerId)));

    if (!customer) {
      return createErrorResponse('Not Found', 'Customer not found', 404);
    }

    const queryValidation = validateQueryParams(request, purchaseQuerySchema);
    if (!queryValidation.success) {
      return createErrorResponse(
        queryValidation.error.error,
        queryValidation.error.message,
        400,
        queryValidation.error.details
      );
    }

    const { limit, offset, sortBy, sortOrder } = queryValidation.data;

    // Build order by - ensure we have a valid column
    const validSortBy = sortBy || 'purchaseDate';
    const orderDirection = sortOrder === 'desc' ? desc : asc;

    // Map sortBy to actual columns
    const sortColumns = {
      purchaseDate: customerPurchases.purchaseDate,
      amountPaid: customerPurchases.amountPaid,
      sessionsPurchased: customerPurchases.sessionsPurchased,
    };

    const orderByColumn = sortColumns[validSortBy as keyof typeof sortColumns] || customerPurchases.purchaseDate;

    // Get total count
    const [totalResult] = await db
      .select({ count: count() })
      .from(customerPurchases)
      .where(eq(customerPurchases.customerId, customerId));

    if (!totalResult) {
      throw new Error('Failed to fetch total count');
    }

    const total = totalResult.count;

    // Get purchases with proper type safety
    const purchases = await db
      .select()
      .from(customerPurchases)
      .where(eq(customerPurchases.customerId, customerId))
      .orderBy(orderDirection(orderByColumn))
      .limit(limit || 20)
      .offset(offset || 0);

    // Ensure we have valid limit and offset values
    const validLimit = limit || 20;
    const validOffset = offset || 0;

    const response: PaginatedResponse<PurchaseResponse> = {
      data: purchases.map((purchase) => ({
        id: purchase.id,
        customerId: purchase.customerId,
        sessionsPurchased: purchase.sessionsPurchased,
        amountPaid: purchase.amountPaid,
        purchaseDate: purchase.purchaseDate,
        paymentStatus: purchase.paymentStatus,
        createdAt: purchase.createdAt,
        updatedAt: purchase.updatedAt,
      })),
      pagination: {
        total,
        limit: validLimit,
        offset: validOffset,
        hasMore: validOffset + validLimit < total,
      },
    };

    return createSuccessResponse(response);
  } catch (error) {
    return handleApiError(error);
  }
});

// POST /api/customers/[id]/purchases - Record new purchase
export const POST = withAuth(async (request: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  try {
    const { id: customerId } = await params;
    const trainerId = await getTrainerIdFromUser(user);

    if (!trainerId) {
      return createErrorResponse('Forbidden', 'Invalid trainer access', 403);
    }

    // Verify customer belongs to trainer
    const [customer] = await db
      .select()
      .from(customers)
      .where(and(eq(customers.id, customerId), eq(customers.trainerId, trainerId)));

    if (!customer) {
      return createErrorResponse('Not Found', 'Customer not found', 404);
    }

    const bodyValidation = await validateRequestBody(request, createPurchaseSchema);
    if (!bodyValidation.success) {
      return createErrorResponse(
        bodyValidation.error.error,
        bodyValidation.error.message,
        400,
        bodyValidation.error.details
      );
    }

    const purchaseData = bodyValidation.data;

    // Create purchase record
    const [newPurchase] = await db
      .insert(customerPurchases)
      .values({
        customerId,
        sessionsPurchased: purchaseData.sessionsPurchased,
        amountPaid: purchaseData.amountPaid.toString(),
        paymentStatus: purchaseData.paymentStatus,
      })
      .returning();

    if (!newPurchase) {
      throw new Error('Failed to create purchase');
    }

    // Add credits using credit transaction service (only if payment is completed)
    if (purchaseData.paymentStatus === 'completed') {
      await addCreditsFromPurchase(customerId, newPurchase.id, purchaseData.sessionsPurchased);
    }

    const result = newPurchase;

    if (!result) {
      throw new Error('Failed to create purchase');
    }

    const response: PurchaseResponse = {
      id: result.id,
      customerId: result.customerId,
      sessionsPurchased: result.sessionsPurchased,
      amountPaid: result.amountPaid,
      purchaseDate: result.purchaseDate,
      paymentStatus: result.paymentStatus,
      createdAt: result.createdAt,
      updatedAt: result.updatedAt,
    };

    return createSuccessResponse(response, 201);
  } catch (error) {
    return handleApiError(error);
  }
});
