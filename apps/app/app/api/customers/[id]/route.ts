import { NextRequest } from 'next/server';
import { db, customers } from '@workspace/auth/server';
import { eq, and } from 'drizzle-orm';
import { updateCustomerSchema, type UpdateCustomerInput, type CustomerResponse } from '@/lib/validations';
import {
  withAuth,
  validateRequestBody,
  createSuccessResponse,
  createErrorResponse,
  handleApiError,
  getTrainerIdFromUser,
} from '@/lib/api-utils';

// GET /api/customers/[id] - Get single customer
export const GET = withAuth(async (request: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  try {
    const { id } = await params;
    const trainerId = await getTrainerIdFromUser(user);

    if (!trainerId) {
      return createErrorResponse('Forbidden', 'Invalid trainer access', 403);
    }

    const [customer] = await db
      .select()
      .from(customers)
      .where(and(eq(customers.id, id), eq(customers.trainerId, trainerId)));

    if (!customer) {
      return createErrorResponse('Not Found', 'Customer not found', 404);
    }

    const response: CustomerResponse = {
      id: customer.id,
      trainerId: customer.trainerId,
      name: customer.name,
      email: customer.email,
      phone: customer.phone,
      sessionCredits: customer.sessionCredits,
      createdAt: customer.createdAt,
      updatedAt: customer.updatedAt,
    };

    return createSuccessResponse(response);
  } catch (error) {
    return handleApiError(error);
  }
});

// PUT /api/customers/[id] - Update customer
export const PUT = withAuth(async (request: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  try {
    const { id } = await params;
    const trainerId = await getTrainerIdFromUser(user);

    if (!trainerId) {
      return createErrorResponse('Forbidden', 'Invalid trainer access', 403);
    }

    const bodyValidation = await validateRequestBody(request, updateCustomerSchema);
    if (!bodyValidation.success) {
      return createErrorResponse(
        bodyValidation.error.error,
        bodyValidation.error.message,
        400,
        bodyValidation.error.details
      );
    }

    const updateData = bodyValidation.data;

    // Check if customer exists and belongs to trainer
    const [existingCustomer] = await db
      .select()
      .from(customers)
      .where(and(eq(customers.id, id), eq(customers.trainerId, trainerId)));

    if (!existingCustomer) {
      return createErrorResponse('Not Found', 'Customer not found', 404);
    }

    // Update customer
    const [updatedCustomer] = await db
      .update(customers)
      .set({
        ...updateData,
        email: updateData.email || null,
        phone: updateData.phone || null,
        updatedAt: new Date(),
      })
      .where(eq(customers.id, id))
      .returning();

    if (!updatedCustomer) {
      throw new Error('Failed to update customer');
    }

    const response: CustomerResponse = {
      id: updatedCustomer.id,
      trainerId: updatedCustomer.trainerId,
      name: updatedCustomer.name,
      email: updatedCustomer.email,
      phone: updatedCustomer.phone,
      sessionCredits: updatedCustomer.sessionCredits,
      createdAt: updatedCustomer.createdAt,
      updatedAt: updatedCustomer.updatedAt,
    };

    return createSuccessResponse(response);
  } catch (error) {
    return handleApiError(error);
  }
});

// DELETE /api/customers/[id] - Delete customer
export const DELETE = withAuth(async (request: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  try {
    const { id } = await params;
    const trainerId = await getTrainerIdFromUser(user);

    if (!trainerId) {
      return createErrorResponse('Forbidden', 'Invalid trainer access', 403);
    }

    // Check if customer exists and belongs to trainer
    const [existingCustomer] = await db
      .select()
      .from(customers)
      .where(and(eq(customers.id, id), eq(customers.trainerId, trainerId)));

    if (!existingCustomer) {
      return createErrorResponse('Not Found', 'Customer not found', 404);
    }

    // Delete customer
    await db.delete(customers).where(eq(customers.id, id));

    return createSuccessResponse({ message: 'Customer deleted successfully' });
  } catch (error) {
    return handleApiError(error);
  }
});
