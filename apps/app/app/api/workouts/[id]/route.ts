import { NextRequest } from 'next/server';
import { db, workouts, workoutParticipants, customers } from '@workspace/auth/server';
import { eq, and } from 'drizzle-orm';
import { updateWorkoutSchema, type WorkoutResponse } from '@/lib/validations';
import {
  withAuth,
  validateRequestBody,
  createSuccessResponse,
  createErrorResponse,
  handleApiError,
  getTrainerIdFromUser,
} from '@/lib/api-utils';
import { transitionWorkoutStatus } from '@/lib/session-status-service';

// GET /api/workouts/[id] - Get single workout with participants
export const GET = withAuth(async (_request: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  try {
    const { id } = await params;
    const trainerId = await getTrainerIdFromUser(user);

    if (!trainerId) {
      return createErrorResponse('Forbidden', 'Invalid trainer access', 403);
    }

    // Get workout with participants
    const [workout] = await db
      .select()
      .from(workouts)
      .where(and(eq(workouts.id, id), eq(workouts.trainerId, trainerId)));

    if (!workout) {
      return createErrorResponse('Not Found', 'Workout not found', 404);
    }

    // Get participants
    const participants = await db
      .select({
        id: workoutParticipants.id,
        customerId: workoutParticipants.customerId,
        customerName: customers.name,
        status: workoutParticipants.status,
        enrolledAt: workoutParticipants.enrolledAt,
        confirmedAt: workoutParticipants.confirmedAt,
        creditDeducted: workoutParticipants.creditDeducted,
      })
      .from(workoutParticipants)
      .innerJoin(customers, eq(workoutParticipants.customerId, customers.id))
      .where(eq(workoutParticipants.workoutId, id));

    const response: WorkoutResponse = {
      ...workout,
      participants,
      participantCount: participants.length,
    };

    return createSuccessResponse(response);
  } catch (error) {
    return handleApiError(error);
  }
});

// PUT /api/workouts/[id] - Update workout
export const PUT = withAuth(async (request: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  try {
    const { id } = await params;
    const trainerId = await getTrainerIdFromUser(user);

    if (!trainerId) {
      return createErrorResponse('Forbidden', 'Invalid trainer access', 403);
    }

    const bodyValidation = await validateRequestBody(request, updateWorkoutSchema);
    if (!bodyValidation.success) {
      return createErrorResponse(
        bodyValidation.error.error,
        bodyValidation.error.message,
        400,
        bodyValidation.error.details
      );
    }

    const updateData = bodyValidation.data;

    // Check if workout exists and belongs to trainer
    const [existingWorkout] = await db
      .select()
      .from(workouts)
      .where(and(eq(workouts.id, id), eq(workouts.trainerId, trainerId)));

    if (!existingWorkout) {
      return createErrorResponse('Not Found', 'Workout not found', 404);
    }

    // Handle status change separately using status service
    if (updateData.status !== undefined) {
      const statusResult = await transitionWorkoutStatus(id, updateData.status, trainerId);
      if (!statusResult.success) {
        return createErrorResponse('Bad Request', statusResult.message, 400);
      }
    }

    // Prepare update data (excluding status which is handled above)
    const updateValues: any = {
      updatedAt: new Date(),
    };

    if (updateData.title !== undefined) updateValues.title = updateData.title;
    if (updateData.description !== undefined) updateValues.description = updateData.description || null;
    if (updateData.startTime !== undefined) updateValues.startTime = new Date(updateData.startTime);
    if (updateData.endTime !== undefined) updateValues.endTime = new Date(updateData.endTime);
    if (updateData.minParticipants !== undefined) updateValues.minParticipants = updateData.minParticipants;
    if (updateData.maxParticipants !== undefined) updateValues.maxParticipants = updateData.maxParticipants;
    if (updateData.location !== undefined) updateValues.location = updateData.location || null;

    // Update workout (only if there are non-status fields to update)
    let updatedWorkout = existingWorkout;
    if (Object.keys(updateValues).length > 1) {
      // More than just updatedAt
      const result = await db.update(workouts).set(updateValues).where(eq(workouts.id, id)).returning();
      if (result[0]) {
        updatedWorkout = result[0];
      }
    } else {
      // If only status was updated, get the current workout data
      const result = await db.select().from(workouts).where(eq(workouts.id, id));
      if (result[0]) {
        updatedWorkout = result[0];
      }
    }

    // Get participants for response
    const participants = await db
      .select({
        id: workoutParticipants.id,
        customerId: workoutParticipants.customerId,
        customerName: customers.name,
        status: workoutParticipants.status,
        enrolledAt: workoutParticipants.enrolledAt,
        confirmedAt: workoutParticipants.confirmedAt,
        creditDeducted: workoutParticipants.creditDeducted,
      })
      .from(workoutParticipants)
      .innerJoin(customers, eq(workoutParticipants.customerId, customers.id))
      .where(eq(workoutParticipants.workoutId, id));

    const response = {
      ...updatedWorkout,
      participants,
      participantCount: participants.length,
    };

    return createSuccessResponse(response);
  } catch (error) {
    return handleApiError(error);
  }
});

// DELETE /api/workouts/[id] - Delete workout
export const DELETE = withAuth(async (_request: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  try {
    const { id } = await params;
    const trainerId = await getTrainerIdFromUser(user);

    if (!trainerId) {
      return createErrorResponse('Forbidden', 'Invalid trainer access', 403);
    }

    // Check if workout exists and belongs to trainer
    const [existingWorkout] = await db
      .select()
      .from(workouts)
      .where(and(eq(workouts.id, id), eq(workouts.trainerId, trainerId)));

    if (!existingWorkout) {
      return createErrorResponse('Not Found', 'Workout not found', 404);
    }

    // Delete workout participants first (foreign key constraint)
    await db.delete(workoutParticipants).where(eq(workoutParticipants.workoutId, id));

    // Delete workout
    await db.delete(workouts).where(eq(workouts.id, id));

    return createSuccessResponse({ message: 'Workout deleted successfully' });
  } catch (error) {
    return handleApiError(error);
  }
});
