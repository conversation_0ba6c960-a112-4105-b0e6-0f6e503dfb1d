import { NextRequest } from 'next/server';
import { db, workouts, workoutParticipants, customers } from '@workspace/auth/server';
import { eq, and } from 'drizzle-orm';
import {
  updateParticipantSchema,
  type UpdateParticipantInput,
  type WorkoutParticipantResponse,
} from '@/lib/validations';
import {
  withAuth,
  validateRequestBody,
  createSuccessResponse,
  createErrorResponse,
  handleApiError,
  getTrainerIdFromUser,
} from '@/lib/api-utils';
import { transitionParticipantStatus } from '@/lib/session-status-service';

// PUT /api/workouts/[id]/participants/[participantId] - Update participant status
export const PUT = withAuth(
  async (request: NextRequest, user, { params }: { params: Promise<{ id: string; participantId: string }> }) => {
    try {
      const { id, participantId } = await params;
      const trainerId = await getTrainerIdFromUser(user);

      if (!trainerId) {
        return createErrorResponse('Forbidden', 'Invalid trainer access', 403);
      }

      const bodyValidation = await validateRequestBody(request, updateParticipantSchema);
      if (!bodyValidation.success) {
        return createErrorResponse(
          bodyValidation.error.error,
          bodyValidation.error.message,
          400,
          bodyValidation.error.details
        );
      }

      const { status } = bodyValidation.data;

      // Use status service to handle the transition with business rules
      const statusResult = await transitionParticipantStatus(id, participantId, status, trainerId);

      if (!statusResult.success) {
        return createErrorResponse('Bad Request', statusResult.message, 400);
      }

      // Get updated participant with customer name for response
      const [participantWithCustomer] = await db
        .select({
          id: workoutParticipants.id,
          workoutId: workoutParticipants.workoutId,
          customerId: workoutParticipants.customerId,
          customerName: customers.name,
          status: workoutParticipants.status,
          enrolledAt: workoutParticipants.enrolledAt,
          confirmedAt: workoutParticipants.confirmedAt,
          creditDeducted: workoutParticipants.creditDeducted,
        })
        .from(workoutParticipants)
        .innerJoin(customers, eq(workoutParticipants.customerId, customers.id))
        .where(eq(workoutParticipants.id, participantId));

      return createSuccessResponse(participantWithCustomer);
    } catch (error) {
      return handleApiError(error);
    }
  }
);

// DELETE /api/workouts/[id]/participants/[participantId] - Remove participant from workout
export const DELETE = withAuth(
  async (request: NextRequest, user, { params }: { params: Promise<{ id: string; participantId: string }> }) => {
    try {
      const { id, participantId } = await params;
      const trainerId = await getTrainerIdFromUser(user);

      if (!trainerId) {
        return createErrorResponse('Forbidden', 'Invalid trainer access', 403);
      }

      // Verify workout belongs to trainer
      const [workout] = await db
        .select()
        .from(workouts)
        .where(and(eq(workouts.id, id), eq(workouts.trainerId, trainerId)));

      if (!workout) {
        return createErrorResponse('Not Found', 'Workout not found', 404);
      }

      // Verify participant exists and belongs to this workout
      const [participant] = await db
        .select()
        .from(workoutParticipants)
        .innerJoin(customers, eq(workoutParticipants.customerId, customers.id))
        .where(
          and(
            eq(workoutParticipants.id, participantId),
            eq(workoutParticipants.workoutId, id),
            eq(customers.trainerId, trainerId)
          )
        );

      if (!participant) {
        return createErrorResponse('Not Found', 'Participant not found', 404);
      }

      // Remove participant
      await db.delete(workoutParticipants).where(eq(workoutParticipants.id, participantId));

      return createSuccessResponse({ message: 'Participant removed successfully' });
    } catch (error) {
      return handleApiError(error);
    }
  }
);
