import type { WorkoutResponse } from '@/lib/validations';

/**
 * Check if two time periods overlap
 */
export function doTimePeriodsOverlap(start1: Date, end1: Date, start2: Date, end2: Date): boolean {
  return start1 < end2 && start2 < end1;
}

/**
 * Check if a customer has a conflict with existing workouts
 */
export function hasCustomerConflict(
  customerId: string,
  newWorkoutStart: Date,
  newWorkoutEnd: Date,
  existingWorkouts: WorkoutResponse[],
  excludeWorkoutId?: string
): { hasConflict: boolean; conflictingWorkout?: WorkoutResponse } {
  for (const workout of existingWorkouts) {
    // Skip the workout we're checking against (for updates)
    if (excludeWorkoutId && workout.id === excludeWorkoutId) {
      continue;
    }

    // Check if customer is enrolled in this workout
    const isCustomerEnrolled = workout.participants?.some(
      (participant) => participant.customerId === customerId && participant.status !== 'cancelled'
    );

    if (isCustomerEnrolled) {
      const workoutStart = new Date(workout.startTime);
      const workoutEnd = new Date(workout.endTime);

      // Check for time overlap
      if (doTimePeriodsOverlap(newWorkoutStart, newWorkoutEnd, workoutStart, workoutEnd)) {
        return {
          hasConflict: true,
          conflictingWorkout: workout,
        };
      }
    }
  }

  return { hasConflict: false };
}

/**
 * Get all customers with conflicts for a specific workout time
 */
export function getCustomersWithConflicts(
  customerIds: string[],
  workoutStart: Date,
  workoutEnd: Date,
  existingWorkouts: WorkoutResponse[],
  excludeWorkoutId?: string
): string[] {
  const conflictingCustomers: string[] = [];

  for (const customerId of customerIds) {
    const { hasConflict } = hasCustomerConflict(
      customerId,
      workoutStart,
      workoutEnd,
      existingWorkouts,
      excludeWorkoutId
    );

    if (hasConflict) {
      conflictingCustomers.push(customerId);
    }
  }

  return conflictingCustomers;
}

/**
 * Format conflict message for display
 */
export function formatConflictMessage(customerName: string, conflictingWorkout: WorkoutResponse): string {
  const startTime = new Date(conflictingWorkout.startTime);
  const endTime = new Date(conflictingWorkout.endTime);

  const timeString = `${startTime.toLocaleTimeString([], {
    hour: 'numeric',
    minute: '2-digit',
  })} - ${endTime.toLocaleTimeString([], {
    hour: 'numeric',
    minute: '2-digit',
  })}`;

  return `${customerName} is already scheduled for "${conflictingWorkout.title}" at ${timeString}`;
}

/**
 * Check if a workout time conflicts with existing workouts (for workout scheduling)
 */
export function hasWorkoutTimeConflict(
  newWorkoutStart: Date,
  newWorkoutEnd: Date,
  existingWorkouts: WorkoutResponse[],
  excludeWorkoutId?: string
): { hasConflict: boolean; conflictingWorkouts: WorkoutResponse[] } {
  const conflictingWorkouts: WorkoutResponse[] = [];

  for (const workout of existingWorkouts) {
    // Skip the workout we're checking against (for updates)
    if (excludeWorkoutId && workout.id === excludeWorkoutId) {
      continue;
    }

    const workoutStart = new Date(workout.startTime);
    const workoutEnd = new Date(workout.endTime);

    // Check for time overlap
    if (doTimePeriodsOverlap(newWorkoutStart, newWorkoutEnd, workoutStart, workoutEnd)) {
      conflictingWorkouts.push(workout);
    }
  }

  return {
    hasConflict: conflictingWorkouts.length > 0,
    conflictingWorkouts,
  };
}
