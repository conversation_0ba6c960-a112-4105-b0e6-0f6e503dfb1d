import { toast } from 'sonner';

export interface NotificationOptions {
  title: string;
  message: string;
  type: 'success' | 'error' | 'warning' | 'info';
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}

/**
 * Centralized notification service for the application
 */
export class NotificationService {
  static show(options: NotificationOptions) {
    const { title, message, type, duration = 5000, action } = options;

    const fullMessage = `${title}: ${message}`;

    const toastOptions = {
      duration,
      action: action
        ? {
            label: action.label,
            onClick: action.onClick,
          }
        : undefined,
    };

    switch (type) {
      case 'success':
        toast.success(fullMessage, toastOptions);
        break;
      case 'error':
        toast.error(fullMessage, toastOptions);
        break;
      case 'warning':
        toast.warning(fullMessage, toastOptions);
        break;
      case 'info':
        toast.info(fullMessage, toastOptions);
        break;
    }
  }

  // Session status notifications
  static sessionConfirmed(sessionTitle: string, participantCount: number) {
    this.show({
      type: 'success',
      title: 'Session Confirmed',
      message: `"${sessionTitle}" has been confirmed with ${participantCount} participant${participantCount > 1 ? 's' : ''}. Credits have been deducted automatically.`,
    });
  }

  static sessionCancelled(sessionTitle: string, participantCount: number) {
    this.show({
      type: 'warning',
      title: 'Session Cancelled',
      message: `"${sessionTitle}" has been cancelled. Credits have been refunded to ${participantCount} participant${participantCount > 1 ? 's' : ''}.`,
    });
  }

  static sessionCompleted(sessionTitle: string) {
    this.show({
      type: 'success',
      title: 'Session Completed',
      message: `"${sessionTitle}" has been marked as completed.`,
    });
  }

  // Participant status notifications
  static participantConfirmed(customerName: string, sessionTitle: string) {
    this.show({
      type: 'success',
      title: 'Participant Confirmed',
      message: `${customerName} has been confirmed for "${sessionTitle}". 1 credit has been deducted.`,
    });
  }

  static participantCancelled(customerName: string, sessionTitle: string, creditRefunded: boolean) {
    this.show({
      type: 'warning',
      title: 'Participant Cancelled',
      message: `${customerName} has been cancelled from "${sessionTitle}".${creditRefunded ? ' 1 credit has been refunded.' : ''}`,
    });
  }

  static participantEnrolled(customerName: string, sessionTitle: string) {
    this.show({
      type: 'info',
      title: 'Participant Enrolled',
      message: `${customerName} has been enrolled in "${sessionTitle}".`,
    });
  }

  // Credit transaction notifications
  static creditsPurchased(customerName: string, credits: number, amount: string) {
    this.show({
      type: 'success',
      title: 'Credits Purchased',
      message: `${customerName} purchased ${credits} session credit${credits > 1 ? 's' : ''} for $${amount}.`,
    });
  }

  static creditDeducted(customerName: string, sessionTitle: string, remainingCredits: number) {
    this.show({
      type: 'info',
      title: 'Credit Deducted',
      message: `1 credit deducted from ${customerName} for "${sessionTitle}". Remaining: ${remainingCredits} credit${remainingCredits !== 1 ? 's' : ''}.`,
    });
  }

  static creditRefunded(customerName: string, sessionTitle: string, newBalance: number) {
    this.show({
      type: 'info',
      title: 'Credit Refunded',
      message: `1 credit refunded to ${customerName} for cancelled session "${sessionTitle}". New balance: ${newBalance} credit${newBalance !== 1 ? 's' : ''}.`,
    });
  }

  static creditsAdjusted(customerName: string, adjustment: number, reason: string, newBalance: number) {
    const adjustmentText = adjustment > 0 ? `+${adjustment}` : adjustment.toString();
    this.show({
      type: 'info',
      title: 'Credits Adjusted',
      message: `${customerName}'s credits adjusted by ${adjustmentText} (${reason}). New balance: ${newBalance} credit${newBalance !== 1 ? 's' : ''}.`,
    });
  }

  // Business rule notifications
  static insufficientCredits(customerName: string) {
    this.show({
      type: 'error',
      title: 'Insufficient Credits',
      message: `${customerName} does not have enough session credits to enroll.`,
      action: {
        label: 'Add Credits',
        onClick: () => {
          // Navigate to customer page or open purchase dialog
          window.location.href = '/customers';
        },
      },
    });
  }

  static sessionAtCapacity(sessionTitle: string) {
    this.show({
      type: 'warning',
      title: 'Session Full',
      message: `"${sessionTitle}" is at maximum capacity and cannot accept more participants.`,
    });
  }

  static sessionConflict(customerName: string, conflictingSession: string, conflictTime: string) {
    this.show({
      type: 'error',
      title: 'Schedule Conflict',
      message: `${customerName} has a conflicting session "${conflictingSession}" at ${conflictTime}.`,
    });
  }

  static minimumParticipantsNotMet(sessionTitle: string, current: number, minimum: number) {
    const needed = minimum - current;
    this.show({
      type: 'warning',
      title: 'Minimum Participants Not Met',
      message: `"${sessionTitle}" needs ${needed} more participant${needed > 1 ? 's' : ''} to meet the minimum requirement (${current}/${minimum}).`,
    });
  }

  // System notifications
  static dataRefreshed() {
    this.show({
      type: 'info',
      title: 'Data Refreshed',
      message: 'All data has been refreshed successfully.',
      duration: 2000,
    });
  }

  static operationFailed(operation: string, error?: string) {
    this.show({
      type: 'error',
      title: 'Operation Failed',
      message: `Failed to ${operation}.${error ? ` Error: ${error}` : ''}`,
    });
  }

  static validationError(field: string, message: string) {
    this.show({
      type: 'error',
      title: 'Validation Error',
      message: `${field}: ${message}`,
    });
  }

  // Capacity warnings
  static sessionNearCapacity(sessionTitle: string, current: number, maximum: number) {
    const remaining = maximum - current;
    this.show({
      type: 'warning',
      title: 'Session Near Capacity',
      message: `"${sessionTitle}" has only ${remaining} spot${remaining > 1 ? 's' : ''} remaining (${current}/${maximum}).`,
    });
  }

  static sessionStartingSoon(sessionTitle: string, hoursUntil: number) {
    const timeText =
      hoursUntil < 1
        ? `${Math.round(hoursUntil * 60)} minutes`
        : `${Math.round(hoursUntil)} hour${Math.round(hoursUntil) > 1 ? 's' : ''}`;

    this.show({
      type: 'info',
      title: 'Session Starting Soon',
      message: `"${sessionTitle}" starts in ${timeText}.`,
    });
  }

  // Bulk operations
  static bulkOperationCompleted(operation: string, count: number, successCount: number) {
    const hasFailures = successCount < count;
    this.show({
      type: hasFailures ? 'warning' : 'success',
      title: 'Bulk Operation Completed',
      message: `${operation}: ${successCount}/${count} items processed successfully.${hasFailures ? ` ${count - successCount} failed.` : ''}`,
    });
  }
}

// Convenience exports for common notifications
export const notify = NotificationService;
