import useSWR from 'swr';

export const fetcher = (url: string) =>
  fetch(url).then((res) => {
    if (!res.ok) throw new Error('Failed to fetch');
    return res.json();
  });
import type { CreditTransactionQueryInput, PaginatedResponse } from '@/lib/validations';

export interface CreditTransactionWithCustomer {
  id: string;
  customerId: string;
  customerName: string;
  type: string;
  amount: number;
  balanceBefore: number;
  balanceAfter: number;
  description: string;
  relatedPurchaseId: string | null;
  relatedWorkoutId: string | null;
  relatedParticipantId: string | null;
  createdAt: Date;
}

export function useCreditTransactions(params?: CreditTransactionQueryInput) {
  const searchParams = new URLSearchParams();

  if (params?.customerId) searchParams.set('customerId', params.customerId);
  if (params?.type) searchParams.set('type', params.type);
  if (params?.limit) searchParams.set('limit', params.limit.toString());
  if (params?.offset) searchParams.set('offset', params.offset.toString());
  if (params?.sortBy) searchParams.set('sortBy', params.sortBy);
  if (params?.sortOrder) searchParams.set('sortOrder', params.sortOrder);

  const url = `/api/credit-transactions${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;

  const { data, error, isLoading, mutate } = useSWR<PaginatedResponse<CreditTransactionWithCustomer>>(url, fetcher);

  return {
    data,
    isLoading,
    error: error ? (error instanceof Error ? error.message : 'An error occurred') : null,
    mutate,
  };
}

export function useCreditTransaction(transactionId: string) {
  return useSWR<CreditTransactionWithCustomer>(
    transactionId ? `/api/credit-transactions/${transactionId}` : null,
    fetcher
  );
}
