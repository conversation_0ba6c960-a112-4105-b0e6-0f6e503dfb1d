'use client';

import useSWR, { mutate as globalMutate } from 'swr';
import { toast } from 'sonner';
import type {
  CustomerResponse,
  CreateCustomerInput,
  UpdateCustomerInput,
  CustomerQueryInput,
  PaginatedResponse,
} from '@/lib/validations';

export const fetcher = (url: string) =>
  fetch(url).then((res) => {
    if (!res.ok) throw new Error('Failed to fetch');
    return res.json();
  });

export function getCustomersKey(params?: Partial<CustomerQueryInput>) {
  const searchParams = new URLSearchParams();
  if (params?.search) searchParams.set('search', params.search);
  if (params?.limit) searchParams.set('limit', params.limit.toString());
  if (params?.offset) searchParams.set('offset', params.offset.toString());
  if (params?.sortBy) searchParams.set('sortBy', params.sortBy);
  if (params?.sortOrder) searchParams.set('sortOrder', params.sortOrder);
  return `/api/customers?${searchParams.toString()}`;
}

export function useCustomersList(params?: Partial<CustomerQueryInput>) {
  const key = getCustomersKey(params);
  const { data, error, isLoading, mutate } = useSWR<PaginatedResponse<CustomerResponse>>(key, fetcher);

  return {
    customers: data?.data || [],
    loading: isLoading,
    error: error ? (error instanceof Error ? error.message : 'An error occurred') : null,
    pagination: data?.pagination || { total: 0, limit: 20, offset: 0, hasMore: false },
    mutate,
  };
}

export async function createCustomer(customerData: CreateCustomerInput, params?: Partial<CustomerQueryInput>) {
  try {
    const response = await fetch('/api/customers', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(customerData),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to create customer');
    }
    const newCustomer: CustomerResponse = await response.json();
    toast.success('Customer created successfully');
    await globalMutate(getCustomersKey(params));
    return newCustomer;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'An error occurred';
    toast.error(errorMessage);
    throw error;
  }
}

export async function updateCustomer(
  id: string,
  customerData: UpdateCustomerInput,
  params?: Partial<CustomerQueryInput>
) {
  try {
    const response = await fetch(`/api/customers/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(customerData),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to update customer');
    }
    const updatedCustomer: CustomerResponse = await response.json();
    toast.success('Customer updated successfully');
    await globalMutate(getCustomersKey(params));
    return updatedCustomer;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'An error occurred';
    toast.error(errorMessage);
    throw error;
  }
}

export async function deleteCustomer(id: string, params?: Partial<CustomerQueryInput>) {
  try {
    const response = await fetch(`/api/customers/${id}`, {
      method: 'DELETE',
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to delete customer');
    }
    toast.success('Customer deleted successfully');
    await globalMutate(getCustomersKey(params));
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'An error occurred';
    toast.error(errorMessage);
    throw error;
  }
}
