{"$schema": "https://ui.shadcn.com/schema/registry-item.json", "name": "experiment-06", "type": "registry:style", "dependencies": ["@dnd-kit/core", "@dnd-kit/modifiers", "@dnd-kit/utilities", "@radix-ui/react-avatar", "@radix-ui/react-checkbox", "@radix-ui/react-collapsible", "@radix-ui/react-dialog", "@radix-ui/react-dropdown-menu", "@radix-ui/react-label", "@radix-ui/react-popover", "@radix-ui/react-radio-group", "@radix-ui/react-select", "@radix-ui/react-separator", "@radix-ui/react-slot", "@radix-ui/react-tooltip", "@remixicon/react", "date-fns", "next-themes", "react-day-picker", "sonner"], "devDependencies": ["tw-animate-css"], "files": [{"path": "app/layout.tsx", "content": "import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from \"next/font/google\";\nimport { Toaster } from \"@/components/ui/sonner\";\nimport { ThemeProvider } from \"@/providers/theme-provider\";\nimport { CalendarProvider } from \"@/components/event-calendar/calendar-context\";\nimport \"./globals.css\";\n\nconst fontSans = Geist({\n  variable: \"--font-sans\",\n  subsets: [\"latin\"],\n});\n\nconst fontMono = Geist_Mono({\n  variable: \"--font-mono\",\n  subsets: [\"latin\"],\n});\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\" suppressHydrationWarning>\n      <body\n        className={`${fontSans.variable} ${fontMono.variable} bg-sidebar font-sans antialiased`}\n      >\n        <ThemeProvider\n          attribute=\"class\"\n          defaultTheme=\"system\"\n          enableSystem\n          disableTransitionOnChange\n        >\n          <CalendarProvider>{children}</CalendarProvider>\n          <Toaster />\n        </ThemeProvider>\n      </body>\n    </html>\n  );\n}\n", "type": "registry:page", "target": "app/layout.tsx"}, {"path": "app/page.tsx", "content": "import type { Metada<PERSON> } from \"next\";\n\nexport const metadata: Metadata = {\n  title: \"Experiment 06 - Crafted.is\",\n};\n\nimport { AppSidebar } from \"@/components/app-sidebar\";\nimport { SidebarInset, SidebarProvider } from \"@/components/ui/sidebar\";\nimport BigCalendar from \"@/components/big-calendar\";\n\nexport default function Page() {\n  return (\n    <SidebarProvider>\n      <AppSidebar />\n      <SidebarInset>\n        <div className=\"flex flex-1 flex-col gap-4 p-2 pt-0\">\n          <BigCalendar />\n        </div>\n      </SidebarInset>\n    </SidebarProvider>\n  );\n}\n", "type": "registry:page", "target": "app/dashboard/page.tsx"}, {"path": "components/app-sidebar.tsx", "content": "\"use client\";\n\nimport * as React from \"react\";\nimport Link from \"next/link\";\nimport { RiCheckLine } from \"@remixicon/react\";\nimport { useCalendarContext } from \"@/components/event-calendar/calendar-context\";\nimport { etiquettes } from \"@/components/big-calendar\";\n\nimport { NavUser } from \"@/components/nav-user\";\nimport {\n  Sidebar,\n  SidebarContent,\n  SidebarFooter,\n  SidebarHeader,\n  SidebarMenu,\n  SidebarMenuButton,\n  SidebarMenuItem,\n  SidebarGroup,\n  SidebarGroupLabel,\n  SidebarGroupContent,\n  SidebarTrigger,\n} from \"@/components/ui/sidebar\";\nimport SidebarCalendar from \"@/components/sidebar-calendar\";\nimport { Checkbox } from \"@/components/ui/checkbox\";\n\nconst data = {\n  user: {\n    name: \"Sofia Safier\",\n    email: \"<EMAIL>\",\n    avatar:\n      \"https://raw.githubusercontent.com/origin-space/origin-images/refs/heads/main/exp6/user-01_l4if9t.png\",\n  },\n};\n\nexport function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {\n  const { isColorVisible, toggleColorVisibility } = useCalendarContext();\n  return (\n    <Sidebar\n      variant=\"inset\"\n      {...props}\n      className=\"dark scheme-only-dark max-lg:p-3 lg:pe-1\"\n    >\n      <SidebarHeader>\n        <div className=\"flex justify-between items-center gap-2\">\n          <Link className=\"inline-flex\" href=\"/\">\n            <span className=\"sr-only\">Logo</span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              width=\"32\"\n              height=\"32\"\n              viewBox=\"0 0 32 32\"\n            >\n              <path\n                fill=\"#52525C\"\n                d=\"m10.661.863-2.339 1.04 5.251 11.794L1.521 9.072l-.918 2.39 12.053 4.627-11.794 5.25 1.041 2.34 11.794-5.252L9.071 30.48l2.39.917 4.626-12.052 5.251 11.793 2.339-1.04-5.251-11.795 12.052 4.627.917-2.39-12.052-4.627 11.794-5.25-1.041-2.34-11.794 5.252L22.928 1.52l-2.39-.917-4.626 12.052L10.662.863Z\"\n              />\n              <path\n                fill=\"#F4F4F5\"\n                d=\"M17.28 0h-2.56v12.91L5.591 3.78l-1.81 1.81 9.129 9.129H0v2.56h12.91L3.78 26.409l1.81 1.81 9.129-9.129V32h2.56V19.09l9.128 9.129 1.81-1.81-9.128-9.129H32v-2.56H19.09l9.129-9.129-1.81-1.81-9.129 9.129V0Z\"\n              />\n            </svg>\n          </Link>\n          <SidebarTrigger className=\"text-muted-foreground/80 hover:text-foreground/80 hover:bg-transparent!\" />\n        </div>\n      </SidebarHeader>\n      <SidebarContent className=\"gap-0 mt-3 pt-3 border-t\">\n        <SidebarGroup className=\"px-1\">\n          <SidebarCalendar />\n        </SidebarGroup>\n        <SidebarGroup className=\"px-1 mt-3 pt-4 border-t\">\n          <SidebarGroupLabel className=\"uppercase text-muted-foreground/65\">\n            Calendars\n          </SidebarGroupLabel>\n          <SidebarGroupContent>\n            <SidebarMenu>\n              {etiquettes.map((item) => (\n                <SidebarMenuItem key={item.id}>\n                  <SidebarMenuButton\n                    asChild\n                    className=\"relative rounded-md [&>svg]:size-auto justify-between has-focus-visible:border-ring has-focus-visible:ring-ring/50 has-focus-visible:ring-[3px]\"\n                  >\n                    <span>\n                      <span className=\"font-medium flex items-center justify-between gap-3\">\n                        <Checkbox\n                          id={item.id}\n                          className=\"sr-only peer\"\n                          checked={isColorVisible(item.color)}\n                          onCheckedChange={() =>\n                            toggleColorVisibility(item.color)\n                          }\n                        />\n                        <RiCheckLine\n                          className=\"peer-not-data-[state=checked]:invisible\"\n                          size={16}\n                          aria-hidden=\"true\"\n                        />\n                        <label\n                          htmlFor={item.id}\n                          className=\"peer-not-data-[state=checked]:line-through peer-not-data-[state=checked]:text-muted-foreground/65 after:absolute after:inset-0\"\n                        >\n                          {item.name}\n                        </label>\n                      </span>\n                      <span\n                        className=\"size-1.5 rounded-full bg-(--event-color)\"\n                        style={\n                          {\n                            \"--event-color\": `var(--color-${item.color}-400)`,\n                          } as React.CSSProperties\n                        }\n                      ></span>\n                    </span>\n                  </SidebarMenuButton>\n                </SidebarMenuItem>\n              ))}\n            </SidebarMenu>\n          </SidebarGroupContent>\n        </SidebarGroup>\n      </SidebarContent>\n      <SidebarFooter>\n        <NavUser user={data.user} />\n      </SidebarFooter>\n    </Sidebar>\n  );\n}\n", "type": "registry:component"}, {"path": "components/big-calendar.tsx", "content": "\"use client\";\n\nimport { useState, useMemo } from \"react\";\nimport { addDays, setHours, setMinutes, getDay } from \"date-fns\";\nimport { useCalendarContext } from \"@/components/event-calendar/calendar-context\";\n\nimport {\n  EventCalendar,\n  type CalendarEvent,\n  type EventColor,\n} from \"@/components/event-calendar\";\n\n// Etiquettes data for calendar filtering\nexport const etiquettes = [\n  {\n    id: \"my-events\",\n    name: \"My Events\",\n    color: \"emerald\" as EventColor,\n    isActive: true,\n  },\n  {\n    id: \"marketing-team\",\n    name: \"Marketing Team\",\n    color: \"orange\" as EventColor,\n    isActive: true,\n  },\n  {\n    id: \"interviews\",\n    name: \"Interviews\",\n    color: \"violet\" as EventColor,\n    isActive: true,\n  },\n  {\n    id: \"events-planning\",\n    name: \"Events Planning\",\n    color: \"blue\" as EventColor,\n    isActive: true,\n  },\n  {\n    id: \"holidays\",\n    name: \"Holidays\",\n    color: \"rose\" as EventColor,\n    isActive: true,\n  },\n];\n\n// Function to calculate days until next Sunday\nconst getDaysUntilNextSunday = (date: Date) => {\n  const day = getDay(date); // 0 is Sunday, 6 is Saturday\n  return day === 0 ? 0 : 7 - day; // If today is Sunday, return 0, otherwise calculate days until Sunday\n};\n\n// Store the current date to avoid repeated new Date() calls\nconst currentDate = new Date();\n\n// Calculate the offset once to avoid repeated calculations\nconst daysUntilNextSunday = getDaysUntilNextSunday(currentDate);\n\n// Sample events data with hardcoded times\nconst sampleEvents: CalendarEvent[] = [\n  {\n    id: \"w1-0a\",\n    title: \"Executive Board Meeting\",\n    description: \"Quarterly review with executive team\",\n    start: setMinutes(\n      setHours(addDays(currentDate, -13 + daysUntilNextSunday), 9),\n      0,\n    ),\n    end: setMinutes(\n      setHours(addDays(currentDate, -13 + daysUntilNextSunday), 11),\n      30,\n    ),\n    color: \"blue\",\n    location: \"Executive Boardroom\",\n  },\n  {\n    id: \"w1-0b\",\n    title: \"Investor Call\",\n    description: \"Update investors on company progress\",\n    start: setMinutes(\n      setHours(addDays(currentDate, -13 + daysUntilNextSunday), 14),\n      0,\n    ),\n    end: setMinutes(\n      setHours(addDays(currentDate, -13 + daysUntilNextSunday), 15),\n      0,\n    ),\n    color: \"violet\",\n    location: \"Conference Room A\",\n  },\n  {\n    id: \"w1-1\",\n    title: \"Strategy Workshop\",\n    description: \"Annual strategy planning session\",\n    start: setMinutes(\n      setHours(addDays(currentDate, -12 + daysUntilNextSunday), 8),\n      30,\n    ),\n    end: setMinutes(\n      setHours(addDays(currentDate, -12 + daysUntilNextSunday), 10),\n      0,\n    ),\n    color: \"violet\",\n    location: \"Innovation Lab\",\n  },\n  {\n    id: \"w1-2\",\n    title: \"Client Presentation\",\n    description: \"Present quarterly results\",\n    start: setMinutes(\n      setHours(addDays(currentDate, -12 + daysUntilNextSunday), 13),\n      0,\n    ),\n    end: setMinutes(\n      setHours(addDays(currentDate, -12 + daysUntilNextSunday), 14),\n      30,\n    ),\n    color: \"emerald\",\n    location: \"Client HQ\",\n  },\n  {\n    id: \"w1-3\",\n    title: \"Budget Review\",\n    description: \"Review department budgets\",\n    start: setMinutes(\n      setHours(addDays(currentDate, -11 + daysUntilNextSunday), 9),\n      15,\n    ),\n    end: setMinutes(\n      setHours(addDays(currentDate, -11 + daysUntilNextSunday), 11),\n      0,\n    ),\n    color: \"blue\",\n    location: \"Finance Room\",\n  },\n  {\n    id: \"w1-4\",\n    title: \"Team Lunch\",\n    description: \"Quarterly team lunch\",\n    start: setMinutes(\n      setHours(addDays(currentDate, -11 + daysUntilNextSunday), 12),\n      0,\n    ),\n    end: setMinutes(\n      setHours(addDays(currentDate, -11 + daysUntilNextSunday), 13),\n      30,\n    ),\n    color: \"orange\",\n    location: \"Bistro Garden\",\n  },\n  {\n    id: \"w1-5\",\n    title: \"Project Kickoff\",\n    description: \"Launch new marketing campaign\",\n    start: setMinutes(\n      setHours(addDays(currentDate, -10 + daysUntilNextSunday), 10),\n      0,\n    ),\n    end: setMinutes(\n      setHours(addDays(currentDate, -10 + daysUntilNextSunday), 12),\n      0,\n    ),\n    color: \"orange\",\n    location: \"Marketing Suite\",\n  },\n  {\n    id: \"w1-6\",\n    title: \"Interview: UX Designer\",\n    description: \"First round interview\",\n    start: setMinutes(\n      setHours(addDays(currentDate, -10 + daysUntilNextSunday), 14),\n      0,\n    ),\n    end: setMinutes(\n      setHours(addDays(currentDate, -10 + daysUntilNextSunday), 15),\n      0,\n    ),\n    color: \"violet\",\n    location: \"HR Office\",\n  },\n  {\n    id: \"w1-7\",\n    title: \"Company All-Hands\",\n    description: \"Monthly company update\",\n    start: setMinutes(\n      setHours(addDays(currentDate, -9 + daysUntilNextSunday), 9),\n      0,\n    ),\n    end: setMinutes(\n      setHours(addDays(currentDate, -9 + daysUntilNextSunday), 10),\n      30,\n    ),\n    color: \"emerald\",\n    location: \"Main Auditorium\",\n  },\n  {\n    id: \"w1-8\",\n    title: \"Product Demo\",\n    description: \"Demo new features to stakeholders\",\n    start: setMinutes(\n      setHours(addDays(currentDate, -9 + daysUntilNextSunday), 13),\n      45,\n    ),\n    end: setMinutes(\n      setHours(addDays(currentDate, -9 + daysUntilNextSunday), 15),\n      0,\n    ),\n    color: \"blue\",\n    location: \"Demo Room\",\n  },\n  {\n    id: \"w1-9\",\n    title: \"Family Time\",\n    description: \"Morning routine with kids\",\n    start: setMinutes(\n      setHours(addDays(currentDate, -8 + daysUntilNextSunday), 7),\n      0,\n    ),\n    end: setMinutes(\n      setHours(addDays(currentDate, -8 + daysUntilNextSunday), 7),\n      30,\n    ),\n    color: \"rose\",\n  },\n  {\n    id: \"w1-10\",\n    title: \"Family Time\",\n    description: \"Breakfast with family\",\n    start: setMinutes(\n      setHours(addDays(currentDate, -8 + daysUntilNextSunday), 10),\n      0,\n    ),\n    end: setMinutes(\n      setHours(addDays(currentDate, -8 + daysUntilNextSunday), 10),\n      30,\n    ),\n    color: \"rose\",\n  },\n  {\n    id: \"5e\",\n    title: \"Family Time\",\n    description: \"Some time to spend with family\",\n    start: setMinutes(\n      setHours(addDays(currentDate, -7 + daysUntilNextSunday), 10),\n      0,\n    ),\n    end: setMinutes(\n      setHours(addDays(currentDate, -7 + daysUntilNextSunday), 13),\n      30,\n    ),\n    color: \"rose\",\n  },\n  {\n    id: \"1b\",\n    title: \"Meeting w/ Ely\",\n    description: \"Strategic planning for next year\",\n    start: setMinutes(\n      setHours(addDays(currentDate, -6 + daysUntilNextSunday), 7),\n      0,\n    ),\n    end: setMinutes(\n      setHours(addDays(currentDate, -6 + daysUntilNextSunday), 8),\n      0,\n    ),\n    color: \"orange\",\n    location: \"Main Conference Hall\",\n  },\n  {\n    id: \"1c\",\n    title: \"Team Catch-up\",\n    description: \"Weekly team sync\",\n    start: setMinutes(\n      setHours(addDays(currentDate, -6 + daysUntilNextSunday), 8),\n      15,\n    ),\n    end: setMinutes(\n      setHours(addDays(currentDate, -6 + daysUntilNextSunday), 11),\n      0,\n    ),\n    color: \"blue\",\n    location: \"Main Conference Hall\",\n  },\n  {\n    id: \"1d\",\n    title: \"Checkin w/ Pedra\",\n    description: \"Coordinate operations\",\n    start: setMinutes(\n      setHours(addDays(currentDate, -6 + daysUntilNextSunday), 15),\n      0,\n    ),\n    end: setMinutes(\n      setHours(addDays(currentDate, -6 + daysUntilNextSunday), 16),\n      0,\n    ),\n    color: \"blue\",\n    location: \"Main Conference Hall\",\n  },\n  {\n    id: \"1e\",\n    title: \"Teem Intro\",\n    description: \"Introduce team members\",\n    start: setMinutes(\n      setHours(addDays(currentDate, -5 + daysUntilNextSunday), 8),\n      15,\n    ),\n    end: setMinutes(\n      setHours(addDays(currentDate, -5 + daysUntilNextSunday), 9),\n      30,\n    ),\n    color: \"emerald\",\n    location: \"Main Conference Hall\",\n  },\n  {\n    id: \"1f\",\n    title: \"Task Presentation\",\n    description: \"Present tasks\",\n    start: setMinutes(\n      setHours(addDays(currentDate, -5 + daysUntilNextSunday), 10),\n      45,\n    ),\n    end: setMinutes(\n      setHours(addDays(currentDate, -5 + daysUntilNextSunday), 13),\n      30,\n    ),\n    color: \"emerald\",\n    location: \"Main Conference Hall\",\n  },\n  {\n    id: \"5\",\n    title: \"Product Meeting\",\n    description: \"Discuss product requirements\",\n    start: setMinutes(\n      setHours(addDays(currentDate, -4 + daysUntilNextSunday), 9),\n      0,\n    ),\n    end: setMinutes(\n      setHours(addDays(currentDate, -4 + daysUntilNextSunday), 11),\n      30,\n    ),\n    color: \"orange\",\n    location: \"Downtown Cafe\",\n  },\n  {\n    id: \"5b\",\n    title: \"Team Meeting\",\n    description: \"Discuss new project requirements\",\n    start: setMinutes(\n      setHours(addDays(currentDate, -4 + daysUntilNextSunday), 13),\n      30,\n    ),\n    end: setMinutes(\n      setHours(addDays(currentDate, -4 + daysUntilNextSunday), 14),\n      0,\n    ),\n    color: \"violet\",\n    location: \"Downtown Cafe\",\n  },\n  {\n    id: \"5c\",\n    title: \"1:1 w/ Tommy\",\n    description: \"Talent review\",\n    start: setMinutes(\n      setHours(addDays(currentDate, -3 + daysUntilNextSunday), 9),\n      45,\n    ),\n    end: setMinutes(\n      setHours(addDays(currentDate, -3 + daysUntilNextSunday), 10),\n      45,\n    ),\n    color: \"violet\",\n    location: \"Abbey Road Room\",\n  },\n  {\n    id: \"5d\",\n    title: \"Kick-off call\",\n    description: \"Ultra fast call with Sonia\",\n    start: setMinutes(\n      setHours(addDays(currentDate, -3 + daysUntilNextSunday), 11),\n      0,\n    ),\n    end: setMinutes(\n      setHours(addDays(currentDate, -3 + daysUntilNextSunday), 11),\n      30,\n    ),\n    color: \"violet\",\n    location: \"Abbey Road Room\",\n  },\n  {\n    id: \"5ef\",\n    title: \"Weekly Review\",\n    description: \"Manual process review\",\n    start: setMinutes(\n      setHours(addDays(currentDate, -2 + daysUntilNextSunday), 8),\n      45,\n    ),\n    end: setMinutes(\n      setHours(addDays(currentDate, -2 + daysUntilNextSunday), 9),\n      45,\n    ),\n    color: \"blue\",\n  },\n  {\n    id: \"5f\",\n    title: \"Meeting w/ Mike\",\n    description: \"Explore new ideas\",\n    start: setMinutes(\n      setHours(addDays(currentDate, -2 + daysUntilNextSunday), 14),\n      30,\n    ),\n    end: setMinutes(\n      setHours(addDays(currentDate, -2 + daysUntilNextSunday), 15),\n      30,\n    ),\n    color: \"orange\",\n    location: \"Main Conference Hall\",\n  },\n  {\n    id: \"5g\",\n    title: \"Family Time\",\n    description: \"Some time to spend with family\",\n    start: setMinutes(\n      setHours(addDays(currentDate, -1 + daysUntilNextSunday), 7),\n      0,\n    ),\n    end: setMinutes(\n      setHours(addDays(currentDate, -1 + daysUntilNextSunday), 7),\n      30,\n    ),\n    color: \"rose\",\n  },\n  {\n    id: \"w3-1\",\n    title: \"Quarterly Planning\",\n    description: \"Plan next quarter objectives\",\n    start: setMinutes(\n      setHours(addDays(currentDate, daysUntilNextSunday), 9),\n      30,\n    ),\n    end: setMinutes(setHours(addDays(currentDate, daysUntilNextSunday), 12), 0),\n    color: \"blue\",\n    location: \"Planning Room\",\n  },\n  {\n    id: \"w3-2\",\n    title: \"Vendor Meeting\",\n    description: \"Review vendor proposals\",\n    start: setMinutes(\n      setHours(addDays(currentDate, daysUntilNextSunday + 1), 7),\n      0,\n    ),\n    end: setMinutes(\n      setHours(addDays(currentDate, daysUntilNextSunday + 1), 8),\n      30,\n    ),\n    color: \"violet\",\n    location: \"Meeting Room B\",\n  },\n  {\n    id: \"w3-3\",\n    title: \"Design Workshop\",\n    description: \"Brainstorming session for new UI\",\n    start: setMinutes(\n      setHours(addDays(currentDate, daysUntilNextSunday + 1), 10),\n      15,\n    ),\n    end: setMinutes(\n      setHours(addDays(currentDate, daysUntilNextSunday + 1), 12),\n      45,\n    ),\n    color: \"emerald\",\n    location: \"Design Studio\",\n  },\n  {\n    id: \"w3-4\",\n    title: \"Lunch with CEO\",\n    description: \"Informal discussion about company vision\",\n    start: setMinutes(\n      setHours(addDays(currentDate, daysUntilNextSunday + 1), 13),\n      0,\n    ),\n    end: setMinutes(\n      setHours(addDays(currentDate, daysUntilNextSunday + 1), 14),\n      30,\n    ),\n    color: \"orange\",\n    location: \"Executive Dining Room\",\n  },\n  {\n    id: \"w3-5\",\n    title: \"Technical Review\",\n    description: \"Code review with engineering team\",\n    start: setMinutes(\n      setHours(addDays(currentDate, daysUntilNextSunday + 2), 11),\n      0,\n    ),\n    end: setMinutes(\n      setHours(addDays(currentDate, daysUntilNextSunday + 2), 12),\n      30,\n    ),\n    color: \"blue\",\n    location: \"Engineering Lab\",\n  },\n  {\n    id: \"w3-6\",\n    title: \"Customer Call\",\n    description: \"Follow-up with key customer\",\n    start: setMinutes(\n      setHours(addDays(currentDate, daysUntilNextSunday + 2), 15),\n      15,\n    ),\n    end: setMinutes(\n      setHours(addDays(currentDate, daysUntilNextSunday + 2), 16),\n      0,\n    ),\n    color: \"violet\",\n    location: \"Call Center\",\n  },\n  {\n    id: \"w3-7\",\n    title: \"Team Building\",\n    description: \"Offsite team building activity\",\n    start: setMinutes(\n      setHours(addDays(currentDate, daysUntilNextSunday + 3), 9),\n      0,\n    ),\n    end: setMinutes(\n      setHours(addDays(currentDate, daysUntilNextSunday + 3), 17),\n      0,\n    ),\n    color: \"emerald\",\n    location: \"Adventure Park\",\n    allDay: true,\n  },\n  {\n    id: \"w3-8\",\n    title: \"Marketing Review\",\n    description: \"Review campaign performance\",\n    start: setMinutes(\n      setHours(addDays(currentDate, daysUntilNextSunday + 4), 8),\n      45,\n    ),\n    end: setMinutes(\n      setHours(addDays(currentDate, daysUntilNextSunday + 4), 10),\n      15,\n    ),\n    color: \"orange\",\n    location: \"Marketing Room\",\n  },\n  {\n    id: \"w3-9\",\n    title: \"Product Roadmap\",\n    description: \"Discuss product roadmap for next quarter\",\n    start: setMinutes(\n      setHours(addDays(currentDate, daysUntilNextSunday + 5), 14),\n      0,\n    ),\n    end: setMinutes(\n      setHours(addDays(currentDate, daysUntilNextSunday + 5), 16),\n      30,\n    ),\n    color: \"blue\",\n    location: \"Strategy Room\",\n  },\n  {\n    id: \"w3-10\",\n    title: \"Family Time\",\n    description: \"Morning walk with family\",\n    start: setMinutes(\n      setHours(addDays(currentDate, daysUntilNextSunday + 6), 7),\n      0,\n    ),\n    end: setMinutes(\n      setHours(addDays(currentDate, daysUntilNextSunday + 6), 7),\n      30,\n    ),\n    color: \"rose\",\n  },\n  {\n    id: \"w3-11\",\n    title: \"Family Time\",\n    description: \"Brunch with family\",\n    start: setMinutes(\n      setHours(addDays(currentDate, daysUntilNextSunday + 6), 10),\n      0,\n    ),\n    end: setMinutes(\n      setHours(addDays(currentDate, daysUntilNextSunday + 6), 10),\n      30,\n    ),\n    color: \"rose\",\n  },\n];\n\nexport default function Component() {\n  const [events, setEvents] = useState<CalendarEvent[]>(sampleEvents);\n  const { isColorVisible } = useCalendarContext();\n\n  // Filter events based on visible colors\n  const visibleEvents = useMemo(() => {\n    return events.filter((event) => isColorVisible(event.color));\n  }, [events, isColorVisible]);\n\n  const handleEventAdd = (event: CalendarEvent) => {\n    setEvents([...events, event]);\n  };\n\n  const handleEventUpdate = (updatedEvent: CalendarEvent) => {\n    setEvents(\n      events.map((event) =>\n        event.id === updatedEvent.id ? updatedEvent : event,\n      ),\n    );\n  };\n\n  const handleEventDelete = (eventId: string) => {\n    setEvents(events.filter((event) => event.id !== eventId));\n  };\n\n  return (\n    <EventCalendar\n      events={visibleEvents}\n      onEventAdd={handleEventAdd}\n      onEventUpdate={handleEventUpdate}\n      onEventDelete={handleEventDelete}\n      initialView=\"week\"\n    />\n  );\n}\n", "type": "registry:component"}, {"path": "components/nav-user.tsx", "content": "import {\n  RiExpandUpDownLine,\n  RiUserLine,\n  RiGroupLine,\n  RiSparklingLine,\n  RiLogoutCircleLine,\n} from \"@remixicon/react\";\n\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\";\nimport {\n  SidebarMenu,\n  SidebarMenuButton,\n  SidebarMenuItem,\n} from \"@/components/ui/sidebar\";\n\nexport function NavUser({\n  user,\n}: {\n  user: {\n    name: string;\n    email: string;\n    avatar: string;\n  };\n}) {\n  return (\n    <SidebarMenu>\n      <SidebarMenuItem>\n        <DropdownMenu>\n          <DropdownMenuTrigger asChild>\n            <SidebarMenuButton\n              size=\"lg\"\n              className=\"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground [&>svg]:size-5\"\n            >\n              <Avatar className=\"size-8\">\n                <AvatarImage src={user.avatar} alt={user.name} />\n                <AvatarFallback className=\"rounded-lg\">S</AvatarFallback>\n              </Avatar>\n              <div className=\"grid flex-1 text-left text-sm leading-tight\">\n                <span className=\"truncate font-medium\">{user.name}</span>\n              </div>\n              <RiExpandUpDownLine className=\"ml-auto size-5 text-muted-foreground/80\" />\n            </SidebarMenuButton>\n          </DropdownMenuTrigger>\n          <DropdownMenuContent\n            className=\"w-(--radix-dropdown-menu-trigger-width) dark bg-sidebar\"\n            side=\"bottom\"\n            align=\"end\"\n            sideOffset={4}\n          >\n            <DropdownMenuGroup>\n              <DropdownMenuItem className=\"gap-3 focus:bg-sidebar-accent\">\n                <RiUserLine\n                  size={20}\n                  className=\"size-5 text-muted-foreground/80\"\n                />\n                Profile\n              </DropdownMenuItem>\n              <DropdownMenuItem className=\"gap-3 focus:bg-sidebar-accent\">\n                <RiGroupLine\n                  size={20}\n                  className=\"size-5 text-muted-foreground/80\"\n                />\n                Accounts\n              </DropdownMenuItem>\n              <DropdownMenuItem className=\"gap-3 focus:bg-sidebar-accent\">\n                <RiSparklingLine\n                  size={20}\n                  className=\"size-5 text-muted-foreground/80\"\n                />\n                Upgrade\n              </DropdownMenuItem>\n              <DropdownMenuItem className=\"gap-3 focus:bg-sidebar-accent\">\n                <RiLogoutCircleLine\n                  size={20}\n                  className=\"size-5 text-muted-foreground/80\"\n                />\n                Logout\n              </DropdownMenuItem>\n            </DropdownMenuGroup>\n          </DropdownMenuContent>\n        </DropdownMenu>\n      </SidebarMenuItem>\n    </SidebarMenu>\n  );\n}\n", "type": "registry:component"}, {"path": "components/participants.tsx", "content": "import { Button } from \"@/components/ui/button\";\nimport { Ri<PERSON>oreFill } from \"@remixicon/react\";\n\nexport default function Participants() {\n  return (\n    <div className=\"flex -space-x-[0.45rem]\">\n      <img\n        className=\"ring-background rounded-full ring-1\"\n        src=\"https://raw.githubusercontent.com/origin-space/origin-images/refs/heads/main/exp1/avatar-40-16_zn3ygb.jpg\"\n        width={24}\n        height={24}\n        alt=\"Avatar 01\"\n      />\n      <img\n        className=\"ring-background rounded-full ring-1\"\n        src=\"https://raw.githubusercontent.com/origin-space/origin-images/refs/heads/main/exp1/avatar-40-10_qyybkj.jpg\"\n        width={24}\n        height={24}\n        alt=\"Avatar 02\"\n      />\n      <img\n        className=\"ring-background rounded-full ring-1\"\n        src=\"https://raw.githubusercontent.com/origin-space/origin-images/refs/heads/main/exp1/avatar-40-15_fguzbs.jpg\"\n        width={24}\n        height={24}\n        alt=\"Avatar 03\"\n      />\n      <img\n        className=\"ring-background rounded-full ring-1\"\n        src=\"https://raw.githubusercontent.com/origin-space/origin-images/refs/heads/main/exp1/avatar-40-11_jtjhsp.jpg\"\n        width={24}\n        height={24}\n        alt=\"Avatar 04\"\n      />\n      <Button\n        variant=\"outline\"\n        className=\"flex size-6 items-center justify-center rounded-full text-xs ring-1 ring-background border-transparent shadow-none text-muted-foreground/80 dark:bg-background dark:hover:bg-background dark:border-transparent\"\n        size=\"icon\"\n      >\n        <RiMoreFill className=\"size-4\" size={16} />\n      </Button>\n    </div>\n  );\n}\n", "type": "registry:component"}, {"path": "components/sidebar-calendar.tsx", "content": "\"use client\";\n\nimport { useEffect, useState } from \"react\";\nimport { useCalendarContext } from \"@/components/event-calendar/calendar-context\";\nimport { Calendar } from \"@/components/ui/calendar\";\nimport { cn } from \"@/lib/utils\";\n\ninterface SidebarCalendarProps {\n  className?: string;\n}\n\nexport default function SidebarCalendar({ className }: SidebarCalendarProps) {\n  // Use the shared calendar context\n  const { currentDate, setCurrentDate } = useCalendarContext();\n\n  // Track the month to display in the calendar\n  const [calendarMonth, setCalendarMonth] = useState<Date>(currentDate);\n\n  // Update the calendar month whenever currentDate changes\n  useEffect(() => {\n    setCalendarMonth(currentDate);\n  }, [currentDate]);\n\n  // Handle date selection\n  const handleSelect = (date: Date | undefined) => {\n    if (date) {\n      setCurrentDate(date);\n    }\n  };\n\n  return (\n    <div className={cn(\"w-full flex justify-center\", className)}>\n      <Calendar\n        mode=\"single\"\n        selected={currentDate}\n        onSelect={handleSelect}\n        month={calendarMonth}\n        onMonthChange={setCalendarMonth}\n        classNames={{\n          day_button:\n            \"transition-none! hover:not-in-data-selected:bg-sidebar-accent group-[.range-middle]:group-data-selected:bg-sidebar-accent text-sidebar-foreground\",\n          today: \"*:after:transition-none\",\n          outside: \"data-selected:bg-sidebar-accent/50\",\n        }}\n      />\n    </div>\n  );\n}\n", "type": "registry:component"}, {"path": "components/theme-toggle.tsx", "content": "\"use client\";\n\nimport { RiMoonClearLine, RiSunLine } from \"@remixicon/react\";\nimport { useTheme } from \"next-themes\";\nimport { useId, useState } from \"react\";\n\nexport default function ThemeToggle() {\n  const id = useId();\n  const { theme, setTheme } = useTheme();\n  const [system, setSystem] = useState(false);\n\n  const smartToggle = () => {\n    const prefersDarkScheme = window.matchMedia(\n      \"(prefers-color-scheme: dark)\",\n    ).matches;\n    if (theme === \"system\") {\n      setTheme(prefersDarkScheme ? \"light\" : \"dark\");\n      setSystem(false);\n    } else if (\n      (theme === \"light\" && !prefersDarkScheme) ||\n      (theme === \"dark\" && prefersDarkScheme)\n    ) {\n      setTheme(theme === \"light\" ? \"dark\" : \"light\");\n      setSystem(false);\n    } else {\n      setTheme(\"system\");\n      setSystem(true);\n    }\n  };\n\n  return (\n    <div className=\"flex flex-col justify-center\">\n      <input\n        type=\"checkbox\"\n        name=\"theme-toggle\"\n        id={id}\n        className=\"peer sr-only\"\n        checked={system}\n        onChange={smartToggle}\n        aria-label=\"Toggle dark mode\"\n      />\n      <label\n        className=\"text-muted-foreground/80 hover:text-foreground/80 rounded peer-focus-visible:border-ring peer-focus-visible:ring-ring/50 relative inline-flex size-8 cursor-pointer items-center justify-center transition-[color,box-shadow] outline-none peer-focus-visible:ring-[3px]\"\n        htmlFor={id}\n        aria-hidden=\"true\"\n      >\n        <RiSunLine className=\"dark:hidden\" size={20} aria-hidden=\"true\" />\n        <RiMoonClearLine\n          className=\"hidden dark:block\"\n          size={20}\n          aria-hidden=\"true\"\n        />\n        <span className=\"sr-only\">Switch to system/light/dark version</span>\n      </label>\n    </div>\n  );\n}\n", "type": "registry:component"}, {"path": "components/ui/avatar.tsx", "content": "\"use client\";\n\nimport * as React from \"react\";\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\";\n\nimport { cn } from \"@/lib/utils\";\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className,\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  );\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className,\n      )}\n      {...props}\n    />\n  );\n}\n\nexport { Avatar, AvatarImage, AvatarFallback };\n", "type": "registry:component"}, {"path": "components/ui/button.tsx", "content": "import * as React from \"react\";\nimport { Slot } from \"@radix-ui/react-slot\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  },\n);\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean;\n  }) {\n  const Comp = asChild ? Slot : \"button\";\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  );\n}\n\nexport { Button, buttonVariants };\n", "type": "registry:component"}, {"path": "components/ui/calendar.tsx", "content": "\"use client\";\n\nimport * as React from \"react\";\nimport { ChevronLeftIcon, ChevronRightIcon } from \"lucide-react\";\nimport { DayPicker } from \"react-day-picker\";\n\nimport { cn } from \"@/lib/utils\";\nimport { buttonVariants } from \"@/components/ui/button\";\n\nfunction Calendar({\n  className,\n  classNames,\n  showOutsideDays = true,\n  components: userComponents,\n  ...props\n}: React.ComponentProps<typeof DayPicker>) {\n  const defaultClassNames = {\n    months: \"relative flex flex-col sm:flex-row gap-4\",\n    month: \"w-full\",\n    month_caption:\n      \"relative mx-10 mb-1 flex h-9 items-center justify-center z-20\",\n    caption_label: \"text-sm font-medium\",\n    nav: \"absolute top-0 flex w-full justify-between z-10\",\n    button_previous: cn(\n      buttonVariants({ variant: \"ghost\" }),\n      \"size-8 text-muted-foreground/80 hover:text-foreground p-0\",\n    ),\n    button_next: cn(\n      buttonVariants({ variant: \"ghost\" }),\n      \"size-8 text-muted-foreground/80 hover:text-foreground p-0\",\n    ),\n    weekday: \"size-8 p-0 text-xs font-medium text-muted-foreground/80\",\n    day_button:\n      \"relative flex size-8 items-center justify-center whitespace-nowrap rounded-full p-0 text-foreground group-[[data-selected]:not(.range-middle)]:[transition-property:color,background-color,border-radius,box-shadow] group-[[data-selected]:not(.range-middle)]:duration-150 group-data-disabled:pointer-events-none focus-visible:z-10 hover:not-in-data-selected:bg-accent group-data-selected:bg-primary hover:not-in-data-selected:text-foreground group-data-selected:text-primary-foreground group-data-disabled:text-foreground/30 group-data-disabled:line-through group-data-outside:text-foreground/30 group-data-selected:group-data-outside:text-primary-foreground outline-none focus-visible:ring-ring/50 focus-visible:ring-[3px] group-[.range-start:not(.range-end)]:rounded-e-full group-[.range-end:not(.range-start)]:rounded-s-full group-[.range-middle]:rounded-none group-[.range-middle]:group-data-selected:bg-accent group-[.range-middle]:group-data-selected:text-foreground\",\n    day: \"group size-8 px-0 py-px text-sm relative before:absolute before:inset-y-px before:inset-x-0 [&.range-start:not(.range-end):before]:bg-linear-to-r before:from-transparent before:from-50% before:to-accent before:to-50% [&.range-end:not(.range-start):before]:bg-linear-to-l\",\n    range_start: \"range-start\",\n    range_end: \"range-end\",\n    range_middle: \"range-middle\",\n    today:\n      \"*:after:pointer-events-none *:after:absolute *:after:bottom-1 *:after:start-1/2 *:after:z-10 *:after:size-[3px] *:after:-translate-x-1/2 *:after:rounded-full *:after:bg-primary [&[data-selected]:not(.range-middle)>*]:after:bg-background [&[data-disabled]>*]:after:bg-foreground/30 *:after:transition-colors\",\n    outside:\n      \"text-muted-foreground data-selected:bg-accent/50 data-selected:text-muted-foreground\",\n    hidden: \"invisible\",\n    week_number: \"size-8 p-0 text-xs font-medium text-muted-foreground/80\",\n  };\n\n  const mergedClassNames: typeof defaultClassNames = Object.keys(\n    defaultClassNames,\n  ).reduce(\n    (acc, key) => ({\n      ...acc,\n      [key]: classNames?.[key as keyof typeof classNames]\n        ? cn(\n            defaultClassNames[key as keyof typeof defaultClassNames],\n            classNames[key as keyof typeof classNames],\n          )\n        : defaultClassNames[key as keyof typeof defaultClassNames],\n    }),\n    {} as typeof defaultClassNames,\n  );\n\n  const defaultComponents = {\n    Chevron: (props: {\n      className?: string;\n      size?: number;\n      disabled?: boolean;\n      orientation?: \"left\" | \"right\" | \"up\" | \"down\";\n    }) => {\n      if (props.orientation === \"left\") {\n        return <ChevronLeftIcon size={16} {...props} aria-hidden=\"true\" />;\n      }\n      return <ChevronRightIcon size={16} {...props} aria-hidden=\"true\" />;\n    },\n  };\n\n  const mergedComponents = {\n    ...defaultComponents,\n    ...userComponents,\n  };\n\n  return (\n    <DayPicker\n      showOutsideDays={showOutsideDays}\n      className={cn(\"w-fit\", className)}\n      classNames={mergedClassNames}\n      components={mergedComponents}\n      {...props}\n    />\n  );\n}\n\nexport { Calendar };\n", "type": "registry:component"}, {"path": "components/ui/checkbox.tsx", "content": "\"use client\";\n\nimport * as React from \"react\";\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\";\n\nimport { cn } from \"@/lib/utils\";\n\nfunction Checkbox({\n  className,\n  ...props\n}: React.ComponentProps<typeof CheckboxPrimitive.Root>) {\n  return (\n    <CheckboxPrimitive.Root\n      data-slot=\"checkbox\"\n      className={cn(\n        \"peer border-input data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-500\",\n        className,\n      )}\n      {...props}\n    >\n      <CheckboxPrimitive.Indicator\n        data-slot=\"checkbox-indicator\"\n        className=\"grid place-content-center text-current\"\n      >\n        {props.checked === \"indeterminate\" ? (\n          <svg\n            width=\"9\"\n            height=\"9\"\n            viewBox=\"0 0 9 9\"\n            fill=\"currentcolor\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n          >\n            <path\n              fillRule=\"evenodd\"\n              clipRule=\"evenodd\"\n              d=\"M0.75 4.5C0.75 4.08579 1.08579 3.75 1.5 3.75H7.5C7.91421 3.75 8.25 4.08579 8.25 4.5C8.25 4.91421 7.91421 5.25 7.5 5.25H1.5C1.08579 5.25 0.75 4.91421 0.75 4.5Z\"\n            />\n          </svg>\n        ) : (\n          <svg\n            width=\"9\"\n            height=\"9\"\n            viewBox=\"0 0 9 9\"\n            fill=\"currentcolor\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n          >\n            <path\n              fillRule=\"evenodd\"\n              clipRule=\"evenodd\"\n              d=\"M8.53547 0.62293C8.88226 0.849446 8.97976 1.3142 8.75325 1.66099L4.5083 8.1599C4.38833 8.34356 4.19397 8.4655 3.9764 8.49358C3.75883 8.52167 3.53987 8.45309 3.3772 8.30591L0.616113 5.80777C0.308959 5.52987 0.285246 5.05559 0.563148 4.74844C0.84105 4.44128 1.31533 4.41757 1.62249 4.69547L3.73256 6.60459L7.49741 0.840706C7.72393 0.493916 8.18868 0.396414 8.53547 0.62293Z\"\n            />\n          </svg>\n        )}\n      </CheckboxPrimitive.Indicator>\n    </CheckboxPrimitive.Root>\n  );\n}\n\nexport { Checkbox };\n", "type": "registry:component"}, {"path": "components/ui/collapsible.tsx", "content": "\"use client\";\n\nimport * as CollapsiblePrimitive from \"@radix-ui/react-collapsible\";\n\nfunction Collapsible({\n  ...props\n}: React.ComponentProps<typeof CollapsiblePrimitive.Root>) {\n  return <CollapsiblePrimitive.Root data-slot=\"collapsible\" {...props} />;\n}\n\nfunction CollapsibleTrigger({\n  ...props\n}: React.ComponentProps<typeof CollapsiblePrimitive.CollapsibleTrigger>) {\n  return (\n    <CollapsiblePrimitive.CollapsibleTrigger\n      data-slot=\"collapsible-trigger\"\n      {...props}\n    />\n  );\n}\n\nfunction CollapsibleContent({\n  ...props\n}: React.ComponentProps<typeof CollapsiblePrimitive.CollapsibleContent>) {\n  return (\n    <CollapsiblePrimitive.CollapsibleContent\n      data-slot=\"collapsible-content\"\n      {...props}\n    />\n  );\n}\n\nexport { Collapsible, CollapsibleTrigger, CollapsibleContent };\n", "type": "registry:component"}, {"path": "components/ui/dialog.tsx", "content": "\"use client\";\n\nimport * as React from \"react\";\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\";\nimport { XIcon } from \"lucide-react\";\n\nimport { cn } from \"@/lib/utils\";\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />;\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />;\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />;\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />;\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/80\",\n        className,\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction DialogContent({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content>) {\n  return (\n    <DialogPortal>\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-1/2 left-1/2 z-50 grid max-h-[calc(100%-2rem)] w-full max-w-[calc(100%-2rem)] -translate-x-1/2 -translate-y-1/2 gap-4 overflow-y-auto rounded-xl border p-6 shadow-lg duration-200 sm:max-w-100\",\n          className,\n        )}\n        {...props}\n      >\n        {children}\n        <DialogPrimitive.Close className=\"group focus-visible:border-ring focus-visible:ring-ring/50 absolute top-3 right-3 flex size-7 items-center justify-center rounded transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:pointer-events-none\">\n          <XIcon\n            size={16}\n            className=\"opacity-60 transition-opacity group-hover:opacity-100\"\n          />\n          <span className=\"sr-only\">Close</span>\n        </DialogPrimitive.Close>\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  );\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"alert-dialog-header\"\n      className={cn(\"flex flex-col gap-1 text-center sm:text-left\", className)}\n      {...props}\n    />\n  );\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"alert-dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-3 sm:flex-row sm:justify-end\",\n        className,\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"alert-dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  );\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"alert-dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  );\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n};\n", "type": "registry:component"}, {"path": "components/ui/dropdown-menu.tsx", "content": "\"use client\";\n\nimport * as React from \"react\";\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\";\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\";\n\nimport { cn } from \"@/lib/utils\";\n\ntype PointerDownEvent = Parameters<\n  NonNullable<DropdownMenuPrimitive.DropdownMenuContentProps[\"onPointerDown\"]>\n>[0];\ntype PointerDownOutsideEvent = Parameters<\n  NonNullable<\n    DropdownMenuPrimitive.DropdownMenuContentProps[\"onPointerDownOutside\"]\n  >\n>[0];\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />;\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  );\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  );\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  onPointerDown,\n  onPointerDownOutside,\n  onCloseAutoFocus,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  const isCloseFromMouse = React.useRef<boolean>(false);\n\n  const handlePointerDown = React.useCallback(\n    (e: PointerDownEvent) => {\n      isCloseFromMouse.current = true;\n      onPointerDown?.(e);\n    },\n    [onPointerDown],\n  );\n\n  const handlePointerDownOutside = React.useCallback(\n    (e: PointerDownOutsideEvent) => {\n      isCloseFromMouse.current = true;\n      onPointerDownOutside?.(e);\n    },\n    [onPointerDownOutside],\n  );\n\n  const handleCloseAutoFocus = React.useCallback(\n    (e: Event) => {\n      if (onCloseAutoFocus) {\n        return onCloseAutoFocus(e);\n      }\n\n      if (!isCloseFromMouse.current) {\n        return;\n      }\n\n      e.preventDefault();\n      isCloseFromMouse.current = false;\n    },\n    [onCloseAutoFocus],\n  );\n\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-40 overflow-hidden rounded-md border p-1 shadow-lg\",\n          className,\n        )}\n        onPointerDown={handlePointerDown}\n        onPointerDownOutside={handlePointerDownOutside}\n        onCloseAutoFocus={handleCloseAutoFocus}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  );\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  );\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean;\n  variant?: \"default\" | \"destructive\";\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive-foreground data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/40 data-[variant=destructive]:focus:text-destructive-foreground data-[variant=destructive]:*:[svg]:!text-destructive-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0\",\n        className,\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0\",\n        className,\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon size={16} />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  );\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  );\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0\",\n        className,\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  );\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean;\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"text-muted-foreground px-2 py-1.5 text-xs font-medium data-[inset]:pl-8\",\n        className,\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  );\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <kbd\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"bg-background text-muted-foreground/70 ms-auto -me-1 inline-flex h-5 max-h-full items-center rounded border px-1 font-[inherit] text-[0.625rem] font-medium\",\n        className,\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />;\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean;\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className,\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon\n        size={16}\n        className=\"text-muted-foreground/80 ml-auto\"\n      />\n    </DropdownMenuPrimitive.SubTrigger>\n  );\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-40 overflow-hidden rounded-md border p-1 shadow-lg\",\n        className,\n      )}\n      {...props}\n    />\n  );\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuCheckboxItem,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuPortal,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubContent,\n  DropdownMenuSubTrigger,\n  DropdownMenuTrigger,\n};\n", "type": "registry:component"}, {"path": "components/ui/input.tsx", "content": "import * as React from \"react\";\n\nimport { cn } from \"@/lib/utils\";\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"border-input file:text-foreground placeholder:text-muted-foreground/70 flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-sm shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        type === \"search\" &&\n          \"[&::-webkit-search-cancel-button]:appearance-none [&::-webkit-search-decoration]:appearance-none [&::-webkit-search-results-button]:appearance-none [&::-webkit-search-results-decoration]:appearance-none\",\n        type === \"file\" &&\n          \"text-muted-foreground/70 file:border-input file:text-foreground p-0 pr-3 italic file:me-3 file:h-full file:border-0 file:border-r file:border-solid file:bg-transparent file:px-3 file:text-sm file:font-medium file:not-italic\",\n        className,\n      )}\n      {...props}\n    />\n  );\n}\n\nexport { Input };\n", "type": "registry:component"}, {"path": "components/ui/label.tsx", "content": "\"use client\";\n\nimport * as React from \"react\";\nimport * as LabelPrimitive from \"@radix-ui/react-label\";\n\nimport { cn } from \"@/lib/utils\";\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"text-foreground text-sm leading-4 font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className,\n      )}\n      {...props}\n    />\n  );\n}\n\nexport { Label };\n", "type": "registry:component"}, {"path": "components/ui/popover.tsx", "content": "\"use client\";\n\nimport * as React from \"react\";\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\";\n\nimport { cn } from \"@/lib/utils\";\n\nfunction Popover({\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Root>) {\n  return <PopoverPrimitive.Root data-slot=\"popover\" {...props} />;\n}\n\nfunction PopoverTrigger({\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Trigger>) {\n  return <PopoverPrimitive.Trigger data-slot=\"popover-trigger\" {...props} />;\n}\n\nfunction PopoverContent({\n  className,\n  align = \"center\",\n  sideOffset = 4,\n  showArrow = false,\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Content> & {\n  showArrow?: boolean;\n}) {\n  return (\n    <PopoverPrimitive.Portal>\n      <PopoverPrimitive.Content\n        data-slot=\"popover-content\"\n        align={align}\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 rounded-md border p-4 shadow-md outline-hidden\",\n          className,\n        )}\n        {...props}\n      >\n        {props.children}\n        {showArrow && (\n          <PopoverPrimitive.Arrow className=\"fill-popover -my-px drop-shadow-[0_1px_0_hsl(var(--border))]\" />\n        )}\n      </PopoverPrimitive.Content>\n    </PopoverPrimitive.Portal>\n  );\n}\n\nfunction PopoverAnchor({\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Anchor>) {\n  return <PopoverPrimitive.Anchor data-slot=\"popover-anchor\" {...props} />;\n}\n\nexport { Popover, PopoverAnchor, PopoverContent, PopoverTrigger };\n", "type": "registry:component"}, {"path": "components/ui/radio-group.tsx", "content": "\"use client\";\n\nimport * as React from \"react\";\nimport * as RadioGroupPrimitive from \"@radix-ui/react-radio-group\";\n\nimport { cn } from \"@/lib/utils\";\n\nfunction RadioGroup({\n  className,\n  ...props\n}: React.ComponentProps<typeof RadioGroupPrimitive.Root>) {\n  return (\n    <RadioGroupPrimitive.Root\n      data-slot=\"radio-group\"\n      className={cn(\"grid gap-3\", className)}\n      {...props}\n    />\n  );\n}\n\nfunction RadioGroupItem({\n  className,\n  ...props\n}: React.ComponentProps<typeof RadioGroupPrimitive.Item>) {\n  return (\n    <RadioGroupPrimitive.Item\n      data-slot=\"radio-group-item\"\n      className={cn(\n        \"border-input data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive aspect-square size-4 shrink-0 rounded-full border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\n        className,\n      )}\n      {...props}\n    >\n      <RadioGroupPrimitive.Indicator className=\"flex items-center justify-center text-current\">\n        <svg\n          width=\"6\"\n          height=\"6\"\n          viewBox=\"0 0 6 6\"\n          fill=\"currentcolor\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n        >\n          <circle cx=\"3\" cy=\"3\" r=\"3\" />\n        </svg>\n      </RadioGroupPrimitive.Indicator>\n    </RadioGroupPrimitive.Item>\n  );\n}\n\nexport { RadioGroup, RadioGroupItem };\n", "type": "registry:component"}, {"path": "components/ui/select.tsx", "content": "\"use client\";\n\nimport * as React from \"react\";\nimport * as SelectPrimitive from \"@radix-ui/react-select\";\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\";\n\nimport { cn } from \"@/lib/utils\";\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />;\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />;\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />;\n}\n\nfunction SelectTrigger({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger>) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      className={cn(\n        \"border-input text-foreground data-[placeholder]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive flex h-9 w-full items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&>span]:line-clamp-1\",\n        className,\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon\n          size={16}\n          className=\"text-muted-foreground/80 in-aria-invalid:text-destructive/80 shrink-0\"\n        />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  );\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"border-input bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-[min(24rem,var(--radix-select-content-available-height))] min-w-32 overflow-hidden rounded-md border shadow-lg [&_[role=group]]:py-1\",\n          position === \"popper\" &&\n            \"w-full min-w-[var(--radix-select-trigger-width)] data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className,\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" && \"h-[var(--radix-select-trigger-height)]\",\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  );\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\n        \"text-muted-foreground py-1.5 ps-8 pe-2 text-xs font-medium\",\n        className,\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex w-full cursor-default items-center rounded py-1.5 ps-8 pe-2 text-sm outline-hidden select-none data-disabled:pointer-events-none data-disabled:opacity-50\",\n        className,\n      )}\n      {...props}\n    >\n      <span className=\"absolute start-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon size={16} />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  );\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  );\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"text-muted-foreground/80 flex cursor-default items-center justify-center py-1\",\n        className,\n      )}\n      {...props}\n    >\n      <ChevronUpIcon size={16} />\n    </SelectPrimitive.ScrollUpButton>\n  );\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"text-muted-foreground/80 flex cursor-default items-center justify-center py-1\",\n        className,\n      )}\n      {...props}\n    >\n      <ChevronDownIcon size={16} />\n    </SelectPrimitive.ScrollDownButton>\n  );\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n};\n", "type": "registry:component"}, {"path": "components/ui/separator.tsx", "content": "\"use client\";\n\nimport * as React from \"react\";\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\";\n\nimport { cn } from \"@/lib/utils\";\n\nfunction Separator({\n  className,\n  orientation = \"horizontal\",\n  decorative = true,\n  ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n  return (\n    <SeparatorPrimitive.Root\n      data-slot=\"separator-root\"\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\n        className,\n      )}\n      {...props}\n    />\n  );\n}\n\nexport { Separator };\n", "type": "registry:component"}, {"path": "components/ui/sheet.tsx", "content": "\"use client\";\n\nimport * as React from \"react\";\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\";\nimport { XIcon } from \"lucide-react\";\n\nimport { cn } from \"@/lib/utils\";\n\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />;\n}\n\nfunction SheetTrigger({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />;\n}\n\nfunction SheetClose({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />;\n}\n\nfunction SheetPortal({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />;\n}\n\nfunction SheetOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\n  return (\n    <SheetPrimitive.Overlay\n      data-slot=\"sheet-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className,\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction SheetContent({\n  className,\n  children,\n  side = \"right\",\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\";\n}) {\n  return (\n    <SheetPortal>\n      <SheetOverlay />\n      <SheetPrimitive.Content\n        data-slot=\"sheet-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\n          side === \"right\" &&\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\n          side === \"left\" &&\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\n          side === \"top\" &&\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\n          side === \"bottom\" &&\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\n          className,\n        )}\n        {...props}\n      >\n        {children}\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\n          <XIcon className=\"size-4\" />\n          <span className=\"sr-only\">Close</span>\n        </SheetPrimitive.Close>\n      </SheetPrimitive.Content>\n    </SheetPortal>\n  );\n}\n\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-header\"\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\n      {...props}\n    />\n  );\n}\n\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-footer\"\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\n      {...props}\n    />\n  );\n}\n\nfunction SheetTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\n  return (\n    <SheetPrimitive.Title\n      data-slot=\"sheet-title\"\n      className={cn(\"text-foreground font-semibold\", className)}\n      {...props}\n    />\n  );\n}\n\nfunction SheetDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\n  return (\n    <SheetPrimitive.Description\n      data-slot=\"sheet-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  );\n}\n\nexport {\n  Sheet,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n};\n", "type": "registry:component"}, {"path": "components/ui/sidebar.tsx", "content": "\"use client\";\n\nimport * as React from \"react\";\nimport { Slot } from \"@radix-ui/react-slot\";\nimport { VariantProps, cva } from \"class-variance-authority\";\nimport { RiLayoutLeft2Line, RiSkipLeftLine } from \"@remixicon/react\";\n\nimport { useIsMobile } from \"@/hooks/use-mobile\";\nimport { cn } from \"@/lib/utils\";\nimport { Button } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Separator } from \"@/components/ui/separator\";\nimport {\n  Sheet,\n  SheetContent,\n  SheetDescription,\n  SheetHeader,\n  SheetTitle,\n} from \"@/components/ui/sheet\";\nimport { Skeleton } from \"@/components/ui/skeleton\";\nimport {\n  Tooltip,\n  TooltipContent,\n  TooltipProvider,\n  TooltipTrigger,\n} from \"@/components/ui/tooltip\";\n\nconst SIDEBAR_COOKIE_NAME = \"sidebar_state\";\nconst SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7;\nconst SIDEBAR_WIDTH = \"16rem\";\nconst SIDEBAR_WIDTH_MOBILE = \"16.5rem\";\nconst SIDEBAR_WIDTH_ICON = \"3rem\";\nconst SIDEBAR_KEYBOARD_SHORTCUT = \"b\";\n\ntype SidebarContextProps = {\n  state: \"expanded\" | \"collapsed\";\n  open: boolean;\n  setOpen: (open: boolean) => void;\n  openMobile: boolean;\n  setOpenMobile: (open: boolean) => void;\n  isMobile: boolean;\n  toggleSidebar: () => void;\n};\n\nconst SidebarContext = React.createContext<SidebarContextProps | null>(null);\n\nfunction useSidebar() {\n  const context = React.useContext(SidebarContext);\n  if (!context) {\n    throw new Error(\"useSidebar must be used within a SidebarProvider.\");\n  }\n\n  return context;\n}\n\nfunction SidebarProvider({\n  defaultOpen = true,\n  open: openProp,\n  onOpenChange: setOpenProp,\n  className,\n  style,\n  children,\n  ...props\n}: React.ComponentProps<\"div\"> & {\n  defaultOpen?: boolean;\n  open?: boolean;\n  onOpenChange?: (open: boolean) => void;\n}) {\n  const isMobile = useIsMobile();\n  const [openMobile, setOpenMobile] = React.useState(false);\n\n  // This is the internal state of the sidebar.\n  // We use openProp and setOpenProp for control from outside the component.\n  const [_open, _setOpen] = React.useState(defaultOpen);\n  const open = openProp ?? _open;\n  const setOpen = React.useCallback(\n    (value: boolean | ((value: boolean) => boolean)) => {\n      const openState = typeof value === \"function\" ? value(open) : value;\n      if (setOpenProp) {\n        setOpenProp(openState);\n      } else {\n        _setOpen(openState);\n      }\n\n      // This sets the cookie to keep the sidebar state.\n      document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`;\n    },\n    [setOpenProp, open],\n  );\n\n  // Helper to toggle the sidebar.\n  const toggleSidebar = React.useCallback(() => {\n    return isMobile ? setOpenMobile((open) => !open) : setOpen((open) => !open);\n  }, [isMobile, setOpen, setOpenMobile]);\n\n  // Adds a keyboard shortcut to toggle the sidebar.\n  React.useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      if (\n        event.key === SIDEBAR_KEYBOARD_SHORTCUT &&\n        (event.metaKey || event.ctrlKey)\n      ) {\n        event.preventDefault();\n        toggleSidebar();\n      }\n    };\n\n    window.addEventListener(\"keydown\", handleKeyDown);\n    return () => window.removeEventListener(\"keydown\", handleKeyDown);\n  }, [toggleSidebar]);\n\n  // We add a state so that we can do data-state=\"expanded\" or \"collapsed\".\n  // This makes it easier to style the sidebar with Tailwind classes.\n  const state = open ? \"expanded\" : \"collapsed\";\n\n  const contextValue = React.useMemo<SidebarContextProps>(\n    () => ({\n      state,\n      open,\n      setOpen,\n      isMobile,\n      openMobile,\n      setOpenMobile,\n      toggleSidebar,\n    }),\n    [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar],\n  );\n\n  return (\n    <SidebarContext.Provider value={contextValue}>\n      <TooltipProvider delayDuration={0}>\n        <div\n          data-slot=\"sidebar-wrapper\"\n          style={\n            {\n              \"--sidebar-width\": SIDEBAR_WIDTH,\n              \"--sidebar-width-icon\": SIDEBAR_WIDTH_ICON,\n              ...style,\n            } as React.CSSProperties\n          }\n          className={cn(\n            \"group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full\",\n            className,\n          )}\n          {...props}\n        >\n          {children}\n        </div>\n      </TooltipProvider>\n    </SidebarContext.Provider>\n  );\n}\n\nfunction Sidebar({\n  side = \"left\",\n  variant = \"sidebar\",\n  collapsible = \"offcanvas\",\n  className,\n  children,\n  ...props\n}: React.ComponentProps<\"div\"> & {\n  side?: \"left\" | \"right\";\n  variant?: \"sidebar\" | \"floating\" | \"inset\";\n  collapsible?: \"offcanvas\" | \"icon\" | \"none\";\n}) {\n  const { isMobile, state, openMobile, setOpenMobile } = useSidebar();\n\n  if (collapsible === \"none\") {\n    return (\n      <div\n        data-slot=\"sidebar\"\n        className={cn(\n          \"bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col\",\n          className,\n        )}\n        {...props}\n      >\n        {children}\n      </div>\n    );\n  }\n\n  if (isMobile) {\n    return (\n      <Sheet open={openMobile} onOpenChange={setOpenMobile} {...props}>\n        <SheetContent\n          data-sidebar=\"sidebar\"\n          data-slot=\"sidebar\"\n          data-mobile=\"true\"\n          className={cn(\n            \"w-(--sidebar-width) bg-sidebar p-0 text-sidebar-foreground [&>button]:hidden border-none\",\n            className,\n          )}\n          style={\n            {\n              \"--sidebar-width\": SIDEBAR_WIDTH_MOBILE,\n            } as React.CSSProperties\n          }\n          side={side}\n        >\n          <SheetHeader className=\"sr-only\">\n            <SheetTitle>Sidebar</SheetTitle>\n            <SheetDescription>Displays the mobile sidebar.</SheetDescription>\n          </SheetHeader>\n          <div className=\"flex h-full w-full flex-col\">{children}</div>\n        </SheetContent>\n      </Sheet>\n    );\n  }\n\n  return (\n    <div\n      className=\"group peer text-sidebar-foreground hidden lg:block\"\n      data-state={state}\n      data-collapsible={state === \"collapsed\" ? collapsible : \"\"}\n      data-variant={variant}\n      data-side={side}\n      data-slot=\"sidebar\"\n    >\n      {/* This is what handles the sidebar gap on desktop */}\n      <div\n        data-slot=\"sidebar-gap\"\n        className={cn(\n          \"relative w-(--sidebar-width) bg-transparent transition-[width] duration-300 ease-[cubic-bezier(0.65,0,0.35,1)]\",\n          \"group-data-[collapsible=offcanvas]:w-0\",\n          \"group-data-[side=right]:rotate-180\",\n          variant === \"floating\" || variant === \"inset\"\n            ? \"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]\"\n            : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon)\",\n        )}\n      />\n      <div\n        data-slot=\"sidebar-container\"\n        className={cn(\n          \"fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-300 ease-[cubic-bezier(0.65,0,0.35,1)] lg:flex\",\n          side === \"left\"\n            ? \"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]\"\n            : \"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]\",\n          // Adjust the padding for floating and inset variants.\n          variant === \"floating\" || variant === \"inset\"\n            ? \"p-3 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]\"\n            : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l\",\n          className,\n        )}\n        {...props}\n      >\n        <div\n          data-sidebar=\"sidebar\"\n          data-slot=\"sidebar-inner\"\n          className=\"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm\"\n        >\n          {children}\n        </div>\n      </div>\n    </div>\n  );\n}\n\nfunction SidebarTrigger({\n  className,\n  onClick,\n  isOutsideSidebar = false,\n  ...props\n}: React.ComponentProps<typeof Button> & {\n  isOutsideSidebar?: boolean;\n}) {\n  const { toggleSidebar } = useSidebar();\n\n  return (\n    <Button\n      data-sidebar=\"trigger\"\n      data-slot=\"sidebar-trigger\"\n      variant=\"ghost\"\n      size=\"icon\"\n      className={cn(\"size-8\", className)}\n      onClick={(event) => {\n        onClick?.(event);\n        toggleSidebar();\n      }}\n      {...props}\n    >\n      {!isOutsideSidebar ? (\n        <RiSkipLeftLine className=\"size-5\" size={20} />\n      ) : (\n        <RiLayoutLeft2Line className=\"size-5\" size={20} />\n      )}\n      <span className=\"sr-only\">\n        {isOutsideSidebar ? \"Collapse sidebar\" : \"Expand sidebar\"}\n      </span>\n    </Button>\n  );\n}\n\nfunction SidebarRail({ className, ...props }: React.ComponentProps<\"button\">) {\n  const { toggleSidebar } = useSidebar();\n\n  return (\n    <button\n      data-sidebar=\"rail\"\n      data-slot=\"sidebar-rail\"\n      aria-label=\"Toggle Sidebar\"\n      tabIndex={-1}\n      onClick={toggleSidebar}\n      title=\"Toggle Sidebar\"\n      className={cn(\n        \"hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-[cubic-bezier(0.65,0,0.35,1)] group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex\",\n        \"in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize\",\n        \"[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize\",\n        \"hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full\",\n        \"[[data-side=left][data-collapsible=offcanvas]_&]:-right-2\",\n        \"[[data-side=right][data-collapsible=offcanvas]_&]:-left-2\",\n        className,\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction SidebarInset({ className, ...props }: React.ComponentProps<\"main\">) {\n  return (\n    <main\n      data-slot=\"sidebar-inset\"\n      className={cn(\n        \"bg-background relative flex w-full flex-1 flex-col\",\n        \"lg:peer-data-[variant=inset]:m-2 lg:peer-data-[variant=inset]:rounded-2xl lg:peer-data-[variant=inset]:shadow-sm lg:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2\",\n        className,\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction SidebarInput({\n  className,\n  ...props\n}: React.ComponentProps<typeof Input>) {\n  return (\n    <Input\n      data-slot=\"sidebar-input\"\n      data-sidebar=\"input\"\n      className={cn(\"bg-background h-8 w-full shadow-none\", className)}\n      {...props}\n    />\n  );\n}\n\nfunction SidebarHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-header\"\n      data-sidebar=\"header\"\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\n      {...props}\n    />\n  );\n}\n\nfunction SidebarFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-footer\"\n      data-sidebar=\"footer\"\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\n      {...props}\n    />\n  );\n}\n\nfunction SidebarSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof Separator>) {\n  return (\n    <Separator\n      data-slot=\"sidebar-separator\"\n      data-sidebar=\"separator\"\n      className={cn(\"bg-sidebar-border mx-2 w-auto\", className)}\n      {...props}\n    />\n  );\n}\n\nfunction SidebarContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-content\"\n      data-sidebar=\"content\"\n      className={cn(\n        \"flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden\",\n        className,\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction SidebarGroup({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-group\"\n      data-sidebar=\"group\"\n      className={cn(\"relative flex w-full min-w-0 flex-col p-2\", className)}\n      {...props}\n    />\n  );\n}\n\nfunction SidebarGroupLabel({\n  className,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"div\"> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"div\";\n\n  return (\n    <Comp\n      data-slot=\"sidebar-group-label\"\n      data-sidebar=\"group-label\"\n      className={cn(\n        \"text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-300 ease-[cubic-bezier(0.65,0,0.35,1)] focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\n        \"group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0\",\n        className,\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction SidebarGroupAction({\n  className,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"button\";\n\n  return (\n    <Comp\n      data-slot=\"sidebar-group-action\"\n      data-sidebar=\"group-action\"\n      className={cn(\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground absolute top-3.5 right-3 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\n        // Increases the hit area of the button on mobile.\n        \"after:absolute after:-inset-2 lg:after:hidden\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className,\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction SidebarGroupContent({\n  className,\n  ...props\n}: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-group-content\"\n      data-sidebar=\"group-content\"\n      className={cn(\"w-full text-sm\", className)}\n      {...props}\n    />\n  );\n}\n\nfunction SidebarMenu({ className, ...props }: React.ComponentProps<\"ul\">) {\n  return (\n    <ul\n      data-slot=\"sidebar-menu\"\n      data-sidebar=\"menu\"\n      className={cn(\"flex w-full min-w-0 flex-col gap-1\", className)}\n      {...props}\n    />\n  );\n}\n\nfunction SidebarMenuItem({ className, ...props }: React.ComponentProps<\"li\">) {\n  return (\n    <li\n      data-slot=\"sidebar-menu-item\"\n      data-sidebar=\"menu-item\"\n      className={cn(\"group/menu-item relative\", className)}\n      {...props}\n    />\n  );\n}\n\nconst sidebarMenuButtonVariants = cva(\n  \"peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground\",\n        outline:\n          \"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]\",\n      },\n      size: {\n        default: \"h-8 text-sm\",\n        sm: \"h-7 text-xs\",\n        lg: \"h-12 text-sm group-data-[collapsible=icon]:p-0!\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  },\n);\n\nfunction SidebarMenuButton({\n  asChild = false,\n  isActive = false,\n  variant = \"default\",\n  size = \"default\",\n  tooltip,\n  className,\n  ...props\n}: React.ComponentProps<\"button\"> & {\n  asChild?: boolean;\n  isActive?: boolean;\n  tooltip?: string | React.ComponentProps<typeof TooltipContent>;\n} & VariantProps<typeof sidebarMenuButtonVariants>) {\n  const Comp = asChild ? Slot : \"button\";\n  const { isMobile, state } = useSidebar();\n\n  const button = (\n    <Comp\n      data-slot=\"sidebar-menu-button\"\n      data-sidebar=\"menu-button\"\n      data-size={size}\n      data-active={isActive}\n      className={cn(sidebarMenuButtonVariants({ variant, size }), className)}\n      {...props}\n    />\n  );\n\n  if (!tooltip) {\n    return button;\n  }\n\n  if (typeof tooltip === \"string\") {\n    tooltip = {\n      children: tooltip,\n    };\n  }\n\n  return (\n    <Tooltip>\n      <TooltipTrigger asChild>{button}</TooltipTrigger>\n      <TooltipContent\n        side=\"right\"\n        align=\"center\"\n        hidden={state !== \"collapsed\" || isMobile}\n        {...tooltip}\n      />\n    </Tooltip>\n  );\n}\n\nfunction SidebarMenuAction({\n  className,\n  asChild = false,\n  showOnHover = false,\n  ...props\n}: React.ComponentProps<\"button\"> & {\n  asChild?: boolean;\n  showOnHover?: boolean;\n}) {\n  const Comp = asChild ? Slot : \"button\";\n\n  return (\n    <Comp\n      data-slot=\"sidebar-menu-action\"\n      data-sidebar=\"menu-action\"\n      className={cn(\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground peer-hover/menu-button:text-sidebar-accent-foreground absolute top-1.5 right-1 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\n        // Increases the hit area of the button on mobile.\n        \"after:absolute after:-inset-2 lg:after:hidden\",\n        \"peer-data-[size=sm]/menu-button:top-1\",\n        \"peer-data-[size=default]/menu-button:top-1.5\",\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\n        \"group-data-[collapsible=icon]:hidden\",\n        showOnHover &&\n          \"peer-data-[active=true]/menu-button:text-sidebar-accent-foreground group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 lg:opacity-0\",\n        className,\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction SidebarMenuBadge({\n  className,\n  ...props\n}: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-menu-badge\"\n      data-sidebar=\"menu-badge\"\n      className={cn(\n        \"text-sidebar-foreground pointer-events-none absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums select-none\",\n        \"peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground\",\n        \"peer-data-[size=sm]/menu-button:top-1\",\n        \"peer-data-[size=default]/menu-button:top-1.5\",\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className,\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction SidebarMenuSkeleton({\n  className,\n  showIcon = false,\n  ...props\n}: React.ComponentProps<\"div\"> & {\n  showIcon?: boolean;\n}) {\n  // Random width between 50 to 90%.\n  const width = React.useMemo(() => {\n    return `${Math.floor(Math.random() * 40) + 50}%`;\n  }, []);\n\n  return (\n    <div\n      data-slot=\"sidebar-menu-skeleton\"\n      data-sidebar=\"menu-skeleton\"\n      className={cn(\"flex h-8 items-center gap-2 rounded-md px-2\", className)}\n      {...props}\n    >\n      {showIcon && (\n        <Skeleton\n          className=\"size-4 rounded-md\"\n          data-sidebar=\"menu-skeleton-icon\"\n        />\n      )}\n      <Skeleton\n        className=\"h-4 max-w-(--skeleton-width) flex-1\"\n        data-sidebar=\"menu-skeleton-text\"\n        style={\n          {\n            \"--skeleton-width\": width,\n          } as React.CSSProperties\n        }\n      />\n    </div>\n  );\n}\n\nfunction SidebarMenuSub({ className, ...props }: React.ComponentProps<\"ul\">) {\n  return (\n    <ul\n      data-slot=\"sidebar-menu-sub\"\n      data-sidebar=\"menu-sub\"\n      className={cn(\n        \"border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className,\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction SidebarMenuSubItem({\n  className,\n  ...props\n}: React.ComponentProps<\"li\">) {\n  return (\n    <li\n      data-slot=\"sidebar-menu-sub-item\"\n      data-sidebar=\"menu-sub-item\"\n      className={cn(\"group/menu-sub-item relative\", className)}\n      {...props}\n    />\n  );\n}\n\nfunction SidebarMenuSubButton({\n  asChild = false,\n  size = \"md\",\n  isActive = false,\n  className,\n  ...props\n}: React.ComponentProps<\"a\"> & {\n  asChild?: boolean;\n  size?: \"sm\" | \"md\";\n  isActive?: boolean;\n}) {\n  const Comp = asChild ? Slot : \"a\";\n\n  return (\n    <Comp\n      data-slot=\"sidebar-menu-sub-button\"\n      data-sidebar=\"menu-sub-button\"\n      data-size={size}\n      data-active={isActive}\n      className={cn(\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\n        \"data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground\",\n        size === \"sm\" && \"text-xs\",\n        size === \"md\" && \"text-sm\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className,\n      )}\n      {...props}\n    />\n  );\n}\n\nexport {\n  Sidebar,\n  SidebarContent,\n  SidebarFooter,\n  SidebarGroup,\n  SidebarGroupAction,\n  SidebarGroupContent,\n  SidebarGroupLabel,\n  SidebarHeader,\n  SidebarInput,\n  SidebarInset,\n  SidebarMenu,\n  SidebarMenuAction,\n  SidebarMenuBadge,\n  SidebarMenuButton,\n  SidebarMenuItem,\n  SidebarMenuSkeleton,\n  SidebarMenuSub,\n  SidebarMenuSubButton,\n  SidebarMenuSubItem,\n  SidebarProvider,\n  SidebarRail,\n  SidebarSeparator,\n  SidebarTrigger,\n  useSidebar,\n};\n", "type": "registry:component"}, {"path": "components/ui/skeleton.tsx", "content": "import { cn } from \"@/lib/utils\";\n\nfunction Skeleton({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"skeleton\"\n      className={cn(\"bg-accent animate-pulse rounded-md\", className)}\n      {...props}\n    />\n  );\n}\n\nexport { Skeleton };\n", "type": "registry:component"}, {"path": "components/ui/sonner.tsx", "content": "\"use client\";\n\nimport { useTheme } from \"next-themes\";\nimport { Toaster as <PERSON><PERSON>, Toaster<PERSON><PERSON> } from \"sonner\";\n\nconst Toaster = ({ ...props }: ToasterProps) => {\n  const { theme = \"system\" } = useTheme();\n\n  return (\n    <Sonner\n      theme={theme as ToasterProps[\"theme\"]}\n      className=\"toaster group\"\n      style={\n        {\n          \"--normal-bg\": \"var(--popover)\",\n          \"--normal-text\": \"var(--popover-foreground)\",\n          \"--normal-border\": \"var(--border)\",\n        } as React.CSSProperties\n      }\n      toastOptions={{\n        classNames: {\n          description: \"text-muted-foreground!\",\n        },\n      }}\n      {...props}\n    />\n  );\n};\n\nexport { Toaster };\n", "type": "registry:component"}, {"path": "components/ui/textarea.tsx", "content": "import * as React from \"react\";\n\nimport { cn } from \"@/lib/utils\";\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground/70 focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive flex min-h-19.5 w-full rounded-md border bg-transparent px-3 py-2 text-sm shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\n        className,\n      )}\n      {...props}\n    />\n  );\n}\nTextarea.displayName = \"Textarea\";\n\nexport { Textarea };\n", "type": "registry:component"}, {"path": "components/ui/tooltip.tsx", "content": "\"use client\";\n\nimport * as React from \"react\";\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\";\n\nimport { cn } from \"@/lib/utils\";\n\nfunction TooltipProvider({\n  delayDuration = 0,\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {\n  return (\n    <TooltipPrimitive.Provider\n      data-slot=\"tooltip-provider\"\n      delayDuration={delayDuration}\n      {...props}\n    />\n  );\n}\n\nfunction Tooltip({\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Root>) {\n  return (\n    <TooltipProvider>\n      <TooltipPrimitive.Root data-slot=\"tooltip\" {...props} />\n    </TooltipProvider>\n  );\n}\n\nfunction TooltipTrigger({\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {\n  return <TooltipPrimitive.Trigger data-slot=\"tooltip-trigger\" {...props} />;\n}\n\nfunction TooltipContent({\n  className,\n  sideOffset = 0,\n  children,\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Content>) {\n  return (\n    <TooltipPrimitive.Portal>\n      <TooltipPrimitive.Content\n        data-slot=\"tooltip-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance\",\n          className,\n        )}\n        {...props}\n      >\n        {children}\n        <TooltipPrimitive.Arrow className=\"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]\" />\n      </TooltipPrimitive.Content>\n    </TooltipPrimitive.Portal>\n  );\n}\n\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider };\n", "type": "registry:component"}, {"path": "components/event-calendar/agenda-view.tsx", "content": "\"use client\";\n\nimport { useMemo } from \"react\";\nimport { RiCalendarEventLine } from \"@remixicon/react\";\nimport { addDays, format, isToday } from \"date-fns\";\n\nimport {\n  AgendaDaysToShow,\n  CalendarEvent,\n  EventItem,\n  getAgendaEventsForDay,\n} from \"@/components/event-calendar\";\n\ninterface AgendaViewProps {\n  currentDate: Date;\n  events: CalendarEvent[];\n  onEventSelect: (event: CalendarEvent) => void;\n}\n\nexport function AgendaView({\n  currentDate,\n  events,\n  onEventSelect,\n}: AgendaViewProps) {\n  // Show events for the next days based on constant\n  const days = useMemo(() => {\n    console.log(\"Agenda view updating with date:\", currentDate.toISOString());\n    return Array.from({ length: AgendaDaysToShow }, (_, i) =>\n      addDays(new Date(currentDate), i),\n    );\n  }, [currentDate]);\n\n  const handleEventClick = (event: CalendarEvent, e: React.MouseEvent) => {\n    e.stopPropagation();\n    console.log(\"Agenda view event clicked:\", event);\n    onEventSelect(event);\n  };\n\n  // Check if there are any days with events\n  const hasEvents = days.some(\n    (day) => getAgendaEventsForDay(events, day).length > 0,\n  );\n\n  return (\n    <div className=\"border-border/70 border-t ps-4\">\n      {!hasEvents ? (\n        <div className=\"flex min-h-[70svh] flex-col items-center justify-center py-16 text-center\">\n          <RiCalendarEventLine\n            size={32}\n            className=\"text-muted-foreground/50 mb-2\"\n          />\n          <h3 className=\"text-lg font-medium\">No events found</h3>\n          <p className=\"text-muted-foreground\">\n            There are no events scheduled for this time period.\n          </p>\n        </div>\n      ) : (\n        days.map((day) => {\n          const dayEvents = getAgendaEventsForDay(events, day);\n\n          if (dayEvents.length === 0) return null;\n\n          return (\n            <div\n              key={day.toString()}\n              className=\"border-border/70 relative my-12 border-t\"\n            >\n              <span\n                className=\"bg-background absolute -top-3 left-0 flex h-6 items-center pe-4 text-[10px] uppercase data-today:font-medium sm:pe-4 sm:text-xs\"\n                data-today={isToday(day) || undefined}\n              >\n                {format(day, \"d MMM, EEEE\")}\n              </span>\n              <div className=\"mt-6 space-y-2\">\n                {dayEvents.map((event) => (\n                  <EventItem\n                    key={event.id}\n                    event={event}\n                    view=\"agenda\"\n                    onClick={(e) => handleEventClick(event, e)}\n                  />\n                ))}\n              </div>\n            </div>\n          );\n        })\n      )}\n    </div>\n  );\n}\n", "type": "registry:component"}, {"path": "components/event-calendar/calendar-context.tsx", "content": "\"use client\";\n\nimport React, { createContext, useContext, useState, ReactNode } from \"react\";\nimport { etiquettes } from \"@/components/big-calendar\";\n\ninterface CalendarContextType {\n  // Date management\n  currentDate: Date;\n  setCurrentDate: (date: Date) => void;\n\n  // Etiquette visibility management\n  visibleColors: string[];\n  toggleColorVisibility: (color: string) => void;\n  isColorVisible: (color: string | undefined) => boolean;\n}\n\nconst CalendarContext = createContext<CalendarContextType | undefined>(\n  undefined,\n);\n\nexport function useCalendarContext() {\n  const context = useContext(CalendarContext);\n  if (context === undefined) {\n    throw new Error(\n      \"useCalendarContext must be used within a CalendarProvider\",\n    );\n  }\n  return context;\n}\n\ninterface CalendarProviderProps {\n  children: ReactNode;\n}\n\nexport function CalendarProvider({ children }: CalendarProviderProps) {\n  const [currentDate, setCurrentDate] = useState<Date>(new Date());\n\n  // Initialize visibleColors based on the isActive property in etiquettes\n  const [visibleColors, setVisibleColors] = useState<string[]>(() => {\n    // Filter etiquettes to get only those that are active\n    return etiquettes\n      .filter((etiquette) => etiquette.isActive)\n      .map((etiquette) => etiquette.color);\n  });\n\n  // Toggle visibility of a color\n  const toggleColorVisibility = (color: string) => {\n    setVisibleColors((prev) => {\n      if (prev.includes(color)) {\n        return prev.filter((c) => c !== color);\n      } else {\n        return [...prev, color];\n      }\n    });\n  };\n\n  // Check if a color is visible\n  const isColorVisible = (color: string | undefined) => {\n    if (!color) return true; // Events without a color are always visible\n    return visibleColors.includes(color);\n  };\n\n  const value = {\n    currentDate,\n    setCurrentDate,\n    visibleColors,\n    toggleColorVisibility,\n    isColorVisible,\n  };\n\n  return (\n    <CalendarContext.Provider value={value}>\n      {children}\n    </CalendarContext.Provider>\n  );\n}\n", "type": "registry:component"}, {"path": "components/event-calendar/calendar-dnd-context.tsx", "content": "\"use client\";\n\nimport {\n  createContext,\n  useContext,\n  useId,\n  useRef,\n  useState,\n  type ReactNode,\n} from \"react\";\nimport {\n  DndContext,\n  DragOverlay,\n  MouseSensor,\n  PointerSensor,\n  TouchSensor,\n  useSensor,\n  useSensors,\n  type DragEndEvent,\n  type DragOverEvent,\n  type DragStartEvent,\n  type UniqueIdentifier,\n} from \"@dnd-kit/core\";\nimport { addMinutes, differenceInMinutes } from \"date-fns\";\n\nimport { EventItem, type CalendarEvent } from \"@/components/event-calendar\";\n\n// Define the context type\ntype CalendarDndContextType = {\n  activeEvent: CalendarEvent | null;\n  activeId: UniqueIdentifier | null;\n  activeView: \"month\" | \"week\" | \"day\" | null;\n  currentTime: Date | null;\n  eventHeight: number | null;\n  isMultiDay: boolean;\n  multiDayWidth: number | null;\n  dragHandlePosition: {\n    x?: number;\n    y?: number;\n    data?: {\n      isFirstDay?: boolean;\n      isLastDay?: boolean;\n    };\n  } | null;\n};\n\n// Create the context\nconst CalendarDndContext = createContext<CalendarDndContextType>({\n  activeEvent: null,\n  activeId: null,\n  activeView: null,\n  currentTime: null,\n  eventHeight: null,\n  isMultiDay: false,\n  multiDayWidth: null,\n  dragHandlePosition: null,\n});\n\n// Hook to use the context\nexport const useCalendarDnd = () => useContext(CalendarDndContext);\n\n// Props for the provider\ninterface CalendarDndProviderProps {\n  children: ReactNode;\n  onEventUpdate: (event: CalendarEvent) => void;\n}\n\nexport function CalendarDndProvider({\n  children,\n  onEventUpdate,\n}: CalendarDndProviderProps) {\n  const [activeEvent, setActiveEvent] = useState<CalendarEvent | null>(null);\n  const [activeId, setActiveId] = useState<UniqueIdentifier | null>(null);\n  const [activeView, setActiveView] = useState<\"month\" | \"week\" | \"day\" | null>(\n    null,\n  );\n  const [currentTime, setCurrentTime] = useState<Date | null>(null);\n  const [eventHeight, setEventHeight] = useState<number | null>(null);\n  const [isMultiDay, setIsMultiDay] = useState(false);\n  const [multiDayWidth, setMultiDayWidth] = useState<number | null>(null);\n  const [dragHandlePosition, setDragHandlePosition] = useState<{\n    x?: number;\n    y?: number;\n    data?: {\n      isFirstDay?: boolean;\n      isLastDay?: boolean;\n    };\n  } | null>(null);\n\n  // Store original event dimensions\n  const eventDimensions = useRef<{ height: number }>({ height: 0 });\n\n  // Configure sensors for better drag detection\n  const sensors = useSensors(\n    useSensor(MouseSensor, {\n      // Require the mouse to move by 5px before activating\n      activationConstraint: {\n        distance: 5,\n      },\n    }),\n    useSensor(TouchSensor, {\n      // Press delay of 250ms, with tolerance of 5px of movement\n      activationConstraint: {\n        delay: 250,\n        tolerance: 5,\n      },\n    }),\n    useSensor(PointerSensor, {\n      // Require the pointer to move by 5px before activating\n      activationConstraint: {\n        distance: 5,\n      },\n    }),\n  );\n\n  // Generate a stable ID for the DndContext\n  const dndContextId = useId();\n\n  const handleDragStart = (event: DragStartEvent) => {\n    const { active } = event;\n\n    // Add safety check for data.current\n    if (!active.data.current) {\n      console.error(\"Missing data in drag start event\", event);\n      return;\n    }\n\n    const {\n      event: calendarEvent,\n      view,\n      height,\n      isMultiDay: eventIsMultiDay,\n      multiDayWidth: eventMultiDayWidth,\n      dragHandlePosition: eventDragHandlePosition,\n    } = active.data.current as {\n      event: CalendarEvent;\n      view: \"month\" | \"week\" | \"day\";\n      height?: number;\n      isMultiDay?: boolean;\n      multiDayWidth?: number;\n      dragHandlePosition?: {\n        x?: number;\n        y?: number;\n        data?: {\n          isFirstDay?: boolean;\n          isLastDay?: boolean;\n        };\n      };\n    };\n\n    setActiveEvent(calendarEvent);\n    setActiveId(active.id);\n    setActiveView(view);\n    setCurrentTime(new Date(calendarEvent.start));\n    setIsMultiDay(eventIsMultiDay || false);\n    setMultiDayWidth(eventMultiDayWidth || null);\n    setDragHandlePosition(eventDragHandlePosition || null);\n\n    // Store event height if provided\n    if (height) {\n      eventDimensions.current.height = height;\n      setEventHeight(height);\n    }\n  };\n\n  const handleDragOver = (event: DragOverEvent) => {\n    const { over } = event;\n\n    if (over && activeEvent && over.data.current) {\n      const { date, time } = over.data.current as { date: Date; time?: number };\n\n      // Update time for week/day views\n      if (time !== undefined && activeView !== \"month\") {\n        const newTime = new Date(date);\n\n        // Calculate hours and minutes with 15-minute precision\n        const hours = Math.floor(time);\n        const fractionalHour = time - hours;\n\n        // Map to nearest 15 minute interval (0, 0.25, 0.5, 0.75)\n        let minutes = 0;\n        if (fractionalHour < 0.125) minutes = 0;\n        else if (fractionalHour < 0.375) minutes = 15;\n        else if (fractionalHour < 0.625) minutes = 30;\n        else minutes = 45;\n\n        newTime.setHours(hours, minutes, 0, 0);\n\n        // Only update if time has changed\n        if (\n          !currentTime ||\n          newTime.getHours() !== currentTime.getHours() ||\n          newTime.getMinutes() !== currentTime.getMinutes() ||\n          newTime.getDate() !== currentTime.getDate() ||\n          newTime.getMonth() !== currentTime.getMonth() ||\n          newTime.getFullYear() !== currentTime.getFullYear()\n        ) {\n          setCurrentTime(newTime);\n        }\n      } else if (activeView === \"month\") {\n        // For month view, just update the date but preserve time\n        const newTime = new Date(date);\n        if (currentTime) {\n          newTime.setHours(\n            currentTime.getHours(),\n            currentTime.getMinutes(),\n            currentTime.getSeconds(),\n            currentTime.getMilliseconds(),\n          );\n        }\n\n        // Only update if date has changed\n        if (\n          !currentTime ||\n          newTime.getDate() !== currentTime.getDate() ||\n          newTime.getMonth() !== currentTime.getMonth() ||\n          newTime.getFullYear() !== currentTime.getFullYear()\n        ) {\n          setCurrentTime(newTime);\n        }\n      }\n    }\n  };\n\n  const handleDragEnd = (event: DragEndEvent) => {\n    const { active, over } = event;\n\n    // Add robust error checking\n    if (!over || !activeEvent || !currentTime) {\n      // Reset state and exit early\n      setActiveEvent(null);\n      setActiveId(null);\n      setActiveView(null);\n      setCurrentTime(null);\n      setEventHeight(null);\n      setIsMultiDay(false);\n      setMultiDayWidth(null);\n      setDragHandlePosition(null);\n      return;\n    }\n\n    try {\n      // Safely access data with checks\n      if (!active.data.current || !over.data.current) {\n        throw new Error(\"Missing data in drag event\");\n      }\n\n      const activeData = active.data.current as {\n        event?: CalendarEvent;\n        view?: string;\n      };\n      const overData = over.data.current as { date?: Date; time?: number };\n\n      // Verify we have all required data\n      if (!activeData.event || !overData.date) {\n        throw new Error(\"Missing required event data\");\n      }\n\n      const calendarEvent = activeData.event;\n      const date = overData.date;\n      const time = overData.time;\n\n      // Calculate new start time\n      const newStart = new Date(date);\n\n      // If time is provided (for week/day views), set the hours and minutes\n      if (time !== undefined) {\n        const hours = Math.floor(time);\n        const fractionalHour = time - hours;\n\n        // Map to nearest 15 minute interval (0, 0.25, 0.5, 0.75)\n        let minutes = 0;\n        if (fractionalHour < 0.125) minutes = 0;\n        else if (fractionalHour < 0.375) minutes = 15;\n        else if (fractionalHour < 0.625) minutes = 30;\n        else minutes = 45;\n\n        newStart.setHours(hours, minutes, 0, 0);\n      } else {\n        // For month view, preserve the original time from currentTime\n        newStart.setHours(\n          currentTime.getHours(),\n          currentTime.getMinutes(),\n          currentTime.getSeconds(),\n          currentTime.getMilliseconds(),\n        );\n      }\n\n      // Calculate new end time based on the original duration\n      const originalStart = new Date(calendarEvent.start);\n      const originalEnd = new Date(calendarEvent.end);\n      const durationMinutes = differenceInMinutes(originalEnd, originalStart);\n      const newEnd = addMinutes(newStart, durationMinutes);\n\n      // Only update if the start time has actually changed\n      const hasStartTimeChanged =\n        originalStart.getFullYear() !== newStart.getFullYear() ||\n        originalStart.getMonth() !== newStart.getMonth() ||\n        originalStart.getDate() !== newStart.getDate() ||\n        originalStart.getHours() !== newStart.getHours() ||\n        originalStart.getMinutes() !== newStart.getMinutes();\n\n      if (hasStartTimeChanged) {\n        // Update the event only if the time has changed\n        onEventUpdate({\n          ...calendarEvent,\n          start: newStart,\n          end: newEnd,\n        });\n      }\n    } catch (error) {\n      console.error(\"Error in drag end handler:\", error);\n    } finally {\n      // Always reset state\n      setActiveEvent(null);\n      setActiveId(null);\n      setActiveView(null);\n      setCurrentTime(null);\n      setEventHeight(null);\n      setIsMultiDay(false);\n      setMultiDayWidth(null);\n      setDragHandlePosition(null);\n    }\n  };\n\n  return (\n    <DndContext\n      id={dndContextId}\n      sensors={sensors}\n      onDragStart={handleDragStart}\n      onDragOver={handleDragOver}\n      onDragEnd={handleDragEnd}\n    >\n      <CalendarDndContext.Provider\n        value={{\n          activeEvent,\n          activeId,\n          activeView,\n          currentTime,\n          eventHeight,\n          isMultiDay,\n          multiDayWidth,\n          dragHandlePosition,\n        }}\n      >\n        {children}\n\n        <DragOverlay adjustScale={false} dropAnimation={null}>\n          {activeEvent && activeView && (\n            <div\n              style={{\n                height: eventHeight ? `${eventHeight}px` : \"auto\",\n                width:\n                  isMultiDay && multiDayWidth ? `${multiDayWidth}%` : \"100%\",\n                // Remove the transform that was causing the shift\n              }}\n            >\n              <EventItem\n                event={activeEvent}\n                view={activeView}\n                isDragging={true}\n                showTime={activeView !== \"month\"}\n                currentTime={currentTime || undefined}\n                isFirstDay={dragHandlePosition?.data?.isFirstDay !== false}\n                isLastDay={dragHandlePosition?.data?.isLastDay !== false}\n              />\n            </div>\n          )}\n        </DragOverlay>\n      </CalendarDndContext.Provider>\n    </DndContext>\n  );\n}\n", "type": "registry:component"}, {"path": "components/event-calendar/constants.ts", "content": "export const EventHeight = 24;\n\n// Vertical gap between events in pixels - controls spacing in month view\nexport const EventGap = 4;\n\n// Height of hour cells in week and day views - controls the scale of time display\nexport const WeekCellsHeight = 72;\n\n// Number of days to show in the agenda view\nexport const AgendaDaysToShow = 30;\n\n// Start and end hours for the week and day views\nexport const StartHour = 7; // Start at 7 AM\nexport const EndHour = 20; // End at 8 PM\n\n// Default start and end times\nexport const DefaultStartHour = 9; // 9 AM\nexport const DefaultEndHour = 10; // 10 AM\n", "type": "registry:component"}, {"path": "components/event-calendar/day-view.tsx", "content": "\"use client\";\n\nimport React, { useMemo } from \"react\";\nimport {\n  addHours,\n  areIntervalsOverlapping,\n  differenceInMinutes,\n  eachHourOfInterval,\n  format,\n  getHours,\n  getMinutes,\n  isSameDay,\n  startOfDay,\n} from \"date-fns\";\n\nimport {\n  DraggableEvent,\n  DroppableCell,\n  EventItem,\n  isMultiDayEvent,\n  useCurrentTimeIndicator,\n  WeekCellsHeight,\n  type CalendarEvent,\n} from \"@/components/event-calendar\";\nimport { StartHour, EndHour } from \"@/components/event-calendar/constants\";\nimport { cn } from \"@/lib/utils\";\n\ninterface DayViewProps {\n  currentDate: Date;\n  events: CalendarEvent[];\n  onEventSelect: (event: CalendarEvent) => void;\n  onEventCreate: (startTime: Date) => void;\n}\n\ninterface PositionedEvent {\n  event: CalendarEvent;\n  top: number;\n  height: number;\n  left: number;\n  width: number;\n  zIndex: number;\n}\n\nexport function DayView({\n  currentDate,\n  events,\n  onEventSelect,\n  onEventCreate,\n}: DayViewProps) {\n  const hours = useMemo(() => {\n    const dayStart = startOfDay(currentDate);\n    return eachHourOfInterval({\n      start: addHours(dayStart, StartHour),\n      end: addHours(dayStart, EndHour - 1),\n    });\n  }, [currentDate]);\n\n  const dayEvents = useMemo(() => {\n    return events\n      .filter((event) => {\n        const eventStart = new Date(event.start);\n        const eventEnd = new Date(event.end);\n        return (\n          isSameDay(currentDate, eventStart) ||\n          isSameDay(currentDate, eventEnd) ||\n          (currentDate > eventStart && currentDate < eventEnd)\n        );\n      })\n      .sort(\n        (a, b) => new Date(a.start).getTime() - new Date(b.start).getTime(),\n      );\n  }, [currentDate, events]);\n\n  // Filter all-day events\n  const allDayEvents = useMemo(() => {\n    return dayEvents.filter((event) => {\n      // Include explicitly marked all-day events or multi-day events\n      return event.allDay || isMultiDayEvent(event);\n    });\n  }, [dayEvents]);\n\n  // Get only single-day time-based events\n  const timeEvents = useMemo(() => {\n    return dayEvents.filter((event) => {\n      // Exclude all-day events and multi-day events\n      return !event.allDay && !isMultiDayEvent(event);\n    });\n  }, [dayEvents]);\n\n  // Process events to calculate positions\n  const positionedEvents = useMemo(() => {\n    const result: PositionedEvent[] = [];\n    const dayStart = startOfDay(currentDate);\n\n    // Sort events by start time and duration\n    const sortedEvents = [...timeEvents].sort((a, b) => {\n      const aStart = new Date(a.start);\n      const bStart = new Date(b.start);\n      const aEnd = new Date(a.end);\n      const bEnd = new Date(b.end);\n\n      // First sort by start time\n      if (aStart < bStart) return -1;\n      if (aStart > bStart) return 1;\n\n      // If start times are equal, sort by duration (longer events first)\n      const aDuration = differenceInMinutes(aEnd, aStart);\n      const bDuration = differenceInMinutes(bEnd, bStart);\n      return bDuration - aDuration;\n    });\n\n    // Track columns for overlapping events\n    const columns: { event: CalendarEvent; end: Date }[][] = [];\n\n    sortedEvents.forEach((event) => {\n      const eventStart = new Date(event.start);\n      const eventEnd = new Date(event.end);\n\n      // Adjust start and end times if they're outside this day\n      const adjustedStart = isSameDay(currentDate, eventStart)\n        ? eventStart\n        : dayStart;\n      const adjustedEnd = isSameDay(currentDate, eventEnd)\n        ? eventEnd\n        : addHours(dayStart, 24);\n\n      // Calculate top position and height\n      const startHour =\n        getHours(adjustedStart) + getMinutes(adjustedStart) / 60;\n      const endHour = getHours(adjustedEnd) + getMinutes(adjustedEnd) / 60;\n      const top = (startHour - StartHour) * WeekCellsHeight;\n      const height = (endHour - startHour) * WeekCellsHeight;\n\n      // Find a column for this event\n      let columnIndex = 0;\n      let placed = false;\n\n      while (!placed) {\n        const col = columns[columnIndex] || [];\n        if (col.length === 0) {\n          columns[columnIndex] = col;\n          placed = true;\n        } else {\n          const overlaps = col.some((c) =>\n            areIntervalsOverlapping(\n              { start: adjustedStart, end: adjustedEnd },\n              { start: new Date(c.event.start), end: new Date(c.event.end) },\n            ),\n          );\n          if (!overlaps) {\n            placed = true;\n          } else {\n            columnIndex++;\n          }\n        }\n      }\n\n      // Ensure column is initialized before pushing\n      const currentColumn = columns[columnIndex] || [];\n      columns[columnIndex] = currentColumn;\n      currentColumn.push({ event, end: adjustedEnd });\n\n      // First column takes full width, others are indented by 10% and take 90% width\n      const width = columnIndex === 0 ? 1 : 0.9;\n      const left = columnIndex === 0 ? 0 : columnIndex * 0.1;\n\n      result.push({\n        event,\n        top,\n        height,\n        left,\n        width,\n        zIndex: 10 + columnIndex, // Higher columns get higher z-index\n      });\n    });\n\n    return result;\n  }, [currentDate, timeEvents]);\n\n  const handleEventClick = (event: CalendarEvent, e: React.MouseEvent) => {\n    e.stopPropagation();\n    onEventSelect(event);\n  };\n\n  const showAllDaySection = allDayEvents.length > 0;\n  const { currentTimePosition, currentTimeVisible } = useCurrentTimeIndicator(\n    currentDate,\n    \"day\",\n  );\n\n  return (\n    <div data-slot=\"day-view\" className=\"contents\">\n      {showAllDaySection && (\n        <div className=\"border-border/70 bg-muted/50 border-t\">\n          <div className=\"grid grid-cols-[3rem_1fr] sm:grid-cols-[4rem_1fr]\">\n            <div className=\"relative\">\n              <span className=\"text-muted-foreground/70 absolute bottom-0 left-0 h-6 w-16 max-w-full pe-2 text-right text-[10px] sm:pe-4 sm:text-xs\">\n                All day\n              </span>\n            </div>\n            <div className=\"border-border/70 relative border-r p-1 last:border-r-0\">\n              {allDayEvents.map((event) => {\n                const eventStart = new Date(event.start);\n                const eventEnd = new Date(event.end);\n                const isFirstDay = isSameDay(currentDate, eventStart);\n                const isLastDay = isSameDay(currentDate, eventEnd);\n\n                return (\n                  <EventItem\n                    key={`spanning-${event.id}`}\n                    onClick={(e) => handleEventClick(event, e)}\n                    event={event}\n                    view=\"month\"\n                    isFirstDay={isFirstDay}\n                    isLastDay={isLastDay}\n                  >\n                    {/* Always show the title in day view for better usability */}\n                    <div>{event.title}</div>\n                  </EventItem>\n                );\n              })}\n            </div>\n          </div>\n        </div>\n      )}\n\n      <div className=\"border-border/70 grid flex-1 grid-cols-[3rem_1fr] border-t sm:grid-cols-[4rem_1fr] overflow-hidden\">\n        <div>\n          {hours.map((hour, index) => (\n            <div\n              key={hour.toString()}\n              className=\"border-border/70 relative h-[var(--week-cells-height)] border-b last:border-b-0\"\n            >\n              {index > 0 && (\n                <span className=\"bg-background text-muted-foreground/70 absolute -top-3 left-0 flex h-6 w-16 max-w-full items-center justify-end pe-2 text-[10px] sm:pe-4 sm:text-xs\">\n                  {format(hour, \"h a\")}\n                </span>\n              )}\n            </div>\n          ))}\n        </div>\n\n        <div className=\"relative\">\n          {/* Positioned events */}\n          {positionedEvents.map((positionedEvent) => (\n            <div\n              key={positionedEvent.event.id}\n              className=\"absolute z-10 px-0.5\"\n              style={{\n                top: `${positionedEvent.top}px`,\n                height: `${positionedEvent.height}px`,\n                left: `${positionedEvent.left * 100}%`,\n                width: `${positionedEvent.width * 100}%`,\n                zIndex: positionedEvent.zIndex,\n              }}\n            >\n              <div className=\"h-full w-full\">\n                <DraggableEvent\n                  event={positionedEvent.event}\n                  view=\"day\"\n                  onClick={(e) => handleEventClick(positionedEvent.event, e)}\n                  showTime\n                  height={positionedEvent.height}\n                />\n              </div>\n            </div>\n          ))}\n\n          {/* Current time indicator */}\n          {currentTimeVisible && (\n            <div\n              className=\"pointer-events-none absolute right-0 left-0 z-20\"\n              style={{ top: `${currentTimePosition}%` }}\n            >\n              <div className=\"relative flex items-center\">\n                <div className=\"bg-red-500 absolute -left-1 h-2 w-2 rounded-full\"></div>\n                <div className=\"bg-red-500 h-[2px] w-full\"></div>\n              </div>\n            </div>\n          )}\n\n          {/* Time grid */}\n          {hours.map((hour) => {\n            const hourValue = getHours(hour);\n            return (\n              <div\n                key={hour.toString()}\n                className=\"border-border/70 relative h-[var(--week-cells-height)] border-b last:border-b-0\"\n              >\n                {/* Quarter-hour intervals */}\n                {[0, 1, 2, 3].map((quarter) => {\n                  const quarterHourTime = hourValue + quarter * 0.25;\n                  return (\n                    <DroppableCell\n                      key={`${hour.toString()}-${quarter}`}\n                      id={`day-cell-${currentDate.toISOString()}-${quarterHourTime}`}\n                      date={currentDate}\n                      time={quarterHourTime}\n                      className={cn(\n                        \"absolute h-[calc(var(--week-cells-height)/4)] w-full\",\n                        quarter === 0 && \"top-0\",\n                        quarter === 1 &&\n                          \"top-[calc(var(--week-cells-height)/4)]\",\n                        quarter === 2 &&\n                          \"top-[calc(var(--week-cells-height)/4*2)]\",\n                        quarter === 3 &&\n                          \"top-[calc(var(--week-cells-height)/4*3)]\",\n                      )}\n                      onClick={() => {\n                        const startTime = new Date(currentDate);\n                        startTime.setHours(hourValue);\n                        startTime.setMinutes(quarter * 15);\n                        onEventCreate(startTime);\n                      }}\n                    />\n                  );\n                })}\n              </div>\n            );\n          })}\n        </div>\n      </div>\n    </div>\n  );\n}\n", "type": "registry:component"}, {"path": "components/event-calendar/draggable-event.tsx", "content": "\"use client\";\n\nimport { useRef, useState } from \"react\";\nimport { useDraggable } from \"@dnd-kit/core\";\nimport { CSS } from \"@dnd-kit/utilities\";\nimport { differenceInDays } from \"date-fns\";\n\nimport {\n  CalendarEvent,\n  EventItem,\n  useCalendarDnd,\n} from \"@/components/event-calendar\";\n\ninterface DraggableEventProps {\n  event: CalendarEvent;\n  view: \"month\" | \"week\" | \"day\";\n  showTime?: boolean;\n  onClick?: (e: React.MouseEvent) => void;\n  height?: number;\n  isMultiDay?: boolean;\n  multiDayWidth?: number;\n  isFirstDay?: boolean;\n  isLastDay?: boolean;\n  \"aria-hidden\"?: boolean | \"true\" | \"false\";\n}\n\nexport function DraggableEvent({\n  event,\n  view,\n  showTime,\n  onClick,\n  height,\n  isMultiDay,\n  multiDayWidth,\n  isFirstDay = true,\n  isLastDay = true,\n  \"aria-hidden\": ariaHidden,\n}: DraggableEventProps) {\n  const { activeId } = useCalendarDnd();\n  const elementRef = useRef<HTMLDivElement>(null);\n  const [dragHandlePosition, setDragHandlePosition] = useState<{\n    x: number;\n    y: number;\n  } | null>(null);\n\n  // Check if this is a multi-day event\n  const eventStart = new Date(event.start);\n  const eventEnd = new Date(event.end);\n  const isMultiDayEvent =\n    isMultiDay || event.allDay || differenceInDays(eventEnd, eventStart) >= 1;\n\n  const { attributes, listeners, setNodeRef, transform, isDragging } =\n    useDraggable({\n      id: `${event.id}-${view}`,\n      data: {\n        event,\n        view,\n        height: height || elementRef.current?.offsetHeight || null,\n        isMultiDay: isMultiDayEvent,\n        multiDayWidth: multiDayWidth,\n        dragHandlePosition,\n        isFirstDay,\n        isLastDay,\n      },\n    });\n\n  // Handle mouse down to track where on the event the user clicked\n  const handleMouseDown = (e: React.MouseEvent) => {\n    if (elementRef.current) {\n      const rect = elementRef.current.getBoundingClientRect();\n      setDragHandlePosition({\n        x: e.clientX - rect.left,\n        y: e.clientY - rect.top,\n      });\n    }\n  };\n\n  // Don't render if this event is being dragged\n  if (isDragging || activeId === `${event.id}-${view}`) {\n    return (\n      <div\n        ref={setNodeRef}\n        className=\"opacity-0\"\n        style={{ height: height || \"auto\" }}\n      />\n    );\n  }\n\n  const style = transform\n    ? {\n        transform: CSS.Translate.toString(transform),\n        height: height || \"auto\",\n        width:\n          isMultiDayEvent && multiDayWidth ? `${multiDayWidth}%` : undefined,\n      }\n    : {\n        height: height || \"auto\",\n        width:\n          isMultiDayEvent && multiDayWidth ? `${multiDayWidth}%` : undefined,\n      };\n\n  // Handle touch start to track where on the event the user touched\n  const handleTouchStart = (e: React.TouchEvent) => {\n    if (elementRef.current) {\n      const rect = elementRef.current.getBoundingClientRect();\n      const touch = e.touches[0];\n      if (touch) {\n        setDragHandlePosition({\n          x: touch.clientX - rect.left,\n          y: touch.clientY - rect.top,\n        });\n      }\n    }\n  };\n\n  return (\n    <div\n      ref={(node) => {\n        setNodeRef(node);\n        if (elementRef) elementRef.current = node;\n      }}\n      style={style}\n      className=\"touch-none\"\n    >\n      <EventItem\n        event={event}\n        view={view}\n        showTime={showTime}\n        isFirstDay={isFirstDay}\n        isLastDay={isLastDay}\n        isDragging={isDragging}\n        onClick={onClick}\n        onMouseDown={handleMouseDown}\n        onTouchStart={handleTouchStart}\n        dndListeners={listeners}\n        dndAttributes={attributes}\n        aria-hidden={ariaHidden}\n      />\n    </div>\n  );\n}\n", "type": "registry:component"}, {"path": "components/event-calendar/droppable-cell.tsx", "content": "\"use client\";\n\nimport { useDroppable } from \"@dnd-kit/core\";\n\nimport { cn } from \"@/lib/utils\";\nimport { useCalendarDnd } from \"@/components/event-calendar\";\n\ninterface DroppableCellProps {\n  id: string;\n  date: Date;\n  time?: number; // For week/day views, represents hours (e.g., 9.25 for 9:15)\n  children?: React.ReactNode;\n  className?: string;\n  onClick?: () => void;\n}\n\nexport function DroppableCell({\n  id,\n  date,\n  time,\n  children,\n  className,\n  onClick,\n}: DroppableCellProps) {\n  const { activeEvent } = useCalendarDnd();\n\n  const { setNodeRef, isOver } = useDroppable({\n    id,\n    data: {\n      date,\n      time,\n    },\n  });\n\n  // Format time for display in tooltip (only for debugging)\n  const formattedTime =\n    time !== undefined\n      ? `${Math.floor(time)}:${Math.round((time - Math.floor(time)) * 60)\n          .toString()\n          .padStart(2, \"0\")}`\n      : null;\n\n  return (\n    <div\n      ref={setNodeRef}\n      onClick={onClick}\n      className={cn(\n        \"data-dragging:bg-accent flex h-full flex-col px-0.5 py-1 sm:px-1\",\n        className,\n      )}\n      title={formattedTime ? `${formattedTime}` : undefined}\n      data-dragging={isOver && activeEvent ? true : undefined}\n    >\n      {children}\n    </div>\n  );\n}\n", "type": "registry:component"}, {"path": "components/event-calendar/event-dialog.tsx", "content": "\"use client\";\n\nimport { useEffect, useMemo, useState } from \"react\";\nimport { RiCalendarLine, RiDeleteBinLine } from \"@remixicon/react\";\nimport { format, isBefore } from \"date-fns\";\n\nimport type { CalendarEvent, EventColor } from \"@/components/event-calendar\";\nimport { cn } from \"@/lib/utils\";\nimport { Button } from \"@/components/ui/button\";\nimport { Calendar } from \"@/components/ui/calendar\";\nimport { Checkbox } from \"@/components/ui/checkbox\";\nimport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from \"@/components/ui/dialog\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport {\n  Popover,\n  PopoverContent,\n  PopoverTrigger,\n} from \"@/components/ui/popover\";\nimport { RadioGroup, RadioGroupItem } from \"@/components/ui/radio-group\";\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport {\n  StartHour,\n  EndHour,\n  DefaultStartHour,\n  DefaultEndHour,\n} from \"@/components/event-calendar/constants\";\n\ninterface EventDialogProps {\n  event: CalendarEvent | null;\n  isOpen: boolean;\n  onClose: () => void;\n  onSave: (event: CalendarEvent) => void;\n  onDelete: (eventId: string) => void;\n}\n\nexport function EventDialog({\n  event,\n  isOpen,\n  onClose,\n  onSave,\n  onDelete,\n}: EventDialogProps) {\n  const [title, setTitle] = useState(\"\");\n  const [description, setDescription] = useState(\"\");\n  const [startDate, setStartDate] = useState<Date>(new Date());\n  const [endDate, setEndDate] = useState<Date>(new Date());\n  const [startTime, setStartTime] = useState(`${DefaultStartHour}:00`);\n  const [endTime, setEndTime] = useState(`${DefaultEndHour}:00`);\n  const [allDay, setAllDay] = useState(false);\n  const [location, setLocation] = useState(\"\");\n  const [color, setColor] = useState<EventColor>(\"blue\");\n  const [error, setError] = useState<string | null>(null);\n  const [startDateOpen, setStartDateOpen] = useState(false);\n  const [endDateOpen, setEndDateOpen] = useState(false);\n\n  // Debug log to check what event is being passed\n  useEffect(() => {\n    console.log(\"EventDialog received event:\", event);\n  }, [event]);\n\n  useEffect(() => {\n    if (event) {\n      setTitle(event.title || \"\");\n      setDescription(event.description || \"\");\n\n      const start = new Date(event.start);\n      const end = new Date(event.end);\n\n      setStartDate(start);\n      setEndDate(end);\n      setStartTime(formatTimeForInput(start));\n      setEndTime(formatTimeForInput(end));\n      setAllDay(event.allDay || false);\n      setLocation(event.location || \"\");\n      setColor((event.color as EventColor) || \"sky\");\n      setError(null); // Reset error when opening dialog\n    } else {\n      resetForm();\n    }\n  }, [event]);\n\n  const resetForm = () => {\n    setTitle(\"\");\n    setDescription(\"\");\n    setStartDate(new Date());\n    setEndDate(new Date());\n    setStartTime(`${DefaultStartHour}:00`);\n    setEndTime(`${DefaultEndHour}:00`);\n    setAllDay(false);\n    setLocation(\"\");\n    setColor(\"blue\");\n    setError(null);\n  };\n\n  const formatTimeForInput = (date: Date) => {\n    const hours = date.getHours().toString().padStart(2, \"0\");\n    const minutes = Math.floor(date.getMinutes() / 15) * 15;\n    return `${hours}:${minutes.toString().padStart(2, \"0\")}`;\n  };\n\n  // Memoize time options so they're only calculated once\n  const timeOptions = useMemo(() => {\n    const options = [];\n    for (let hour = StartHour; hour <= EndHour; hour++) {\n      for (let minute = 0; minute < 60; minute += 15) {\n        const formattedHour = hour.toString().padStart(2, \"0\");\n        const formattedMinute = minute.toString().padStart(2, \"0\");\n        const value = `${formattedHour}:${formattedMinute}`;\n        // Use a fixed date to avoid unnecessary date object creations\n        const date = new Date(2000, 0, 1, hour, minute);\n        const label = format(date, \"h:mm a\");\n        options.push({ value, label });\n      }\n    }\n    return options;\n  }, []); // Empty dependency array ensures this only runs once\n\n  const handleSave = () => {\n    const start = new Date(startDate);\n    const end = new Date(endDate);\n\n    if (!allDay) {\n      const [startHours = 0, startMinutes = 0] = startTime\n        .split(\":\")\n        .map(Number);\n      const [endHours = 0, endMinutes = 0] = endTime.split(\":\").map(Number);\n\n      if (\n        startHours < StartHour ||\n        startHours > EndHour ||\n        endHours < StartHour ||\n        endHours > EndHour\n      ) {\n        setError(\n          `Selected time must be between ${StartHour}:00 and ${EndHour}:00`,\n        );\n        return;\n      }\n\n      start.setHours(startHours, startMinutes, 0);\n      end.setHours(endHours, endMinutes, 0);\n    } else {\n      start.setHours(0, 0, 0, 0);\n      end.setHours(23, 59, 59, 999);\n    }\n\n    // Validate that end date is not before start date\n    if (isBefore(end, start)) {\n      setError(\"End date cannot be before start date\");\n      return;\n    }\n\n    // Use generic title if empty\n    const eventTitle = title.trim() ? title : \"(no title)\";\n\n    onSave({\n      id: event?.id || \"\",\n      title: eventTitle,\n      description,\n      start,\n      end,\n      allDay,\n      location,\n      color,\n    });\n  };\n\n  const handleDelete = () => {\n    if (event?.id) {\n      onDelete(event.id);\n    }\n  };\n\n  // Updated color options to match types.ts\n  const colorOptions: Array<{\n    value: EventColor;\n    label: string;\n    bgClass: string;\n    borderClass: string;\n  }> = [\n    {\n      value: \"blue\",\n      label: \"Blue\",\n      bgClass: \"bg-blue-400 data-[state=checked]:bg-blue-400\",\n      borderClass: \"border-blue-400 data-[state=checked]:border-blue-400\",\n    },\n    {\n      value: \"violet\",\n      label: \"Violet\",\n      bgClass: \"bg-violet-400 data-[state=checked]:bg-violet-400\",\n      borderClass: \"border-violet-400 data-[state=checked]:border-violet-400\",\n    },\n    {\n      value: \"rose\",\n      label: \"Rose\",\n      bgClass: \"bg-rose-400 data-[state=checked]:bg-rose-400\",\n      borderClass: \"border-rose-400 data-[state=checked]:border-rose-400\",\n    },\n    {\n      value: \"emerald\",\n      label: \"Emerald\",\n      bgClass: \"bg-emerald-400 data-[state=checked]:bg-emerald-400\",\n      borderClass: \"border-emerald-400 data-[state=checked]:border-emerald-400\",\n    },\n    {\n      value: \"orange\",\n      label: \"Orange\",\n      bgClass: \"bg-orange-400 data-[state=checked]:bg-orange-400\",\n      borderClass: \"border-orange-400 data-[state=checked]:border-orange-400\",\n    },\n  ];\n\n  return (\n    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>\n      <DialogContent className=\"sm:max-w-[425px]\">\n        <DialogHeader>\n          <DialogTitle>{event?.id ? \"Edit Event\" : \"Create Event\"}</DialogTitle>\n          <DialogDescription className=\"sr-only\">\n            {event?.id\n              ? \"Edit the details of this event\"\n              : \"Add a new event to your calendar\"}\n          </DialogDescription>\n        </DialogHeader>\n        {error && (\n          <div className=\"bg-destructive/15 text-destructive rounded-md px-3 py-2 text-sm\">\n            {error}\n          </div>\n        )}\n        <div className=\"grid gap-4 py-4\">\n          <div className=\"*:not-first:mt-1.5\">\n            <Label htmlFor=\"title\">Title</Label>\n            <Input\n              id=\"title\"\n              value={title}\n              onChange={(e) => setTitle(e.target.value)}\n            />\n          </div>\n\n          <div className=\"*:not-first:mt-1.5\">\n            <Label htmlFor=\"description\">Description</Label>\n            <Textarea\n              id=\"description\"\n              value={description}\n              onChange={(e) => setDescription(e.target.value)}\n              rows={3}\n            />\n          </div>\n\n          <div className=\"flex gap-4\">\n            <div className=\"flex-1 *:not-first:mt-1.5\">\n              <Label htmlFor=\"start-date\">Start Date</Label>\n              <Popover open={startDateOpen} onOpenChange={setStartDateOpen}>\n                <PopoverTrigger asChild>\n                  <Button\n                    id=\"start-date\"\n                    variant={\"outline\"}\n                    className={cn(\n                      \"group bg-background hover:bg-background border-input w-full justify-between px-3 font-normal outline-offset-0 outline-none focus-visible:outline-[3px]\",\n                      !startDate && \"text-muted-foreground\",\n                    )}\n                  >\n                    <span\n                      className={cn(\n                        \"truncate\",\n                        !startDate && \"text-muted-foreground\",\n                      )}\n                    >\n                      {startDate ? format(startDate, \"PPP\") : \"Pick a date\"}\n                    </span>\n                    <RiCalendarLine\n                      size={16}\n                      className=\"text-muted-foreground/80 shrink-0\"\n                      aria-hidden=\"true\"\n                    />\n                  </Button>\n                </PopoverTrigger>\n                <PopoverContent className=\"w-auto p-2\" align=\"start\">\n                  <Calendar\n                    mode=\"single\"\n                    selected={startDate}\n                    defaultMonth={startDate}\n                    onSelect={(date) => {\n                      if (date) {\n                        setStartDate(date);\n                        // If end date is before the new start date, update it to match the start date\n                        if (isBefore(endDate, date)) {\n                          setEndDate(date);\n                        }\n                        setError(null);\n                        setStartDateOpen(false);\n                      }\n                    }}\n                  />\n                </PopoverContent>\n              </Popover>\n            </div>\n\n            {!allDay && (\n              <div className=\"min-w-28 *:not-first:mt-1.5\">\n                <Label htmlFor=\"start-time\">Start Time</Label>\n                <Select value={startTime} onValueChange={setStartTime}>\n                  <SelectTrigger id=\"start-time\">\n                    <SelectValue placeholder=\"Select time\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {timeOptions.map((option) => (\n                      <SelectItem key={option.value} value={option.value}>\n                        {option.label}\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n              </div>\n            )}\n          </div>\n\n          <div className=\"flex gap-4\">\n            <div className=\"flex-1 *:not-first:mt-1.5\">\n              <Label htmlFor=\"end-date\">End Date</Label>\n              <Popover open={endDateOpen} onOpenChange={setEndDateOpen}>\n                <PopoverTrigger asChild>\n                  <Button\n                    id=\"end-date\"\n                    variant={\"outline\"}\n                    className={cn(\n                      \"group bg-background hover:bg-background border-input w-full justify-between px-3 font-normal outline-offset-0 outline-none focus-visible:outline-[3px]\",\n                      !endDate && \"text-muted-foreground\",\n                    )}\n                  >\n                    <span\n                      className={cn(\n                        \"truncate\",\n                        !endDate && \"text-muted-foreground\",\n                      )}\n                    >\n                      {endDate ? format(endDate, \"PPP\") : \"Pick a date\"}\n                    </span>\n                    <RiCalendarLine\n                      size={16}\n                      className=\"text-muted-foreground/80 shrink-0\"\n                      aria-hidden=\"true\"\n                    />\n                  </Button>\n                </PopoverTrigger>\n                <PopoverContent className=\"w-auto p-2\" align=\"start\">\n                  <Calendar\n                    mode=\"single\"\n                    selected={endDate}\n                    defaultMonth={endDate}\n                    disabled={{ before: startDate }}\n                    onSelect={(date) => {\n                      if (date) {\n                        setEndDate(date);\n                        setError(null);\n                        setEndDateOpen(false);\n                      }\n                    }}\n                  />\n                </PopoverContent>\n              </Popover>\n            </div>\n\n            {!allDay && (\n              <div className=\"min-w-28 *:not-first:mt-1.5\">\n                <Label htmlFor=\"end-time\">End Time</Label>\n                <Select value={endTime} onValueChange={setEndTime}>\n                  <SelectTrigger id=\"end-time\">\n                    <SelectValue placeholder=\"Select time\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {timeOptions.map((option) => (\n                      <SelectItem key={option.value} value={option.value}>\n                        {option.label}\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n              </div>\n            )}\n          </div>\n\n          <div className=\"flex items-center gap-2\">\n            <Checkbox\n              id=\"all-day\"\n              checked={allDay}\n              onCheckedChange={(checked) => setAllDay(checked === true)}\n            />\n            <Label htmlFor=\"all-day\">All day</Label>\n          </div>\n\n          <div className=\"*:not-first:mt-1.5\">\n            <Label htmlFor=\"location\">Location</Label>\n            <Input\n              id=\"location\"\n              value={location}\n              onChange={(e) => setLocation(e.target.value)}\n            />\n          </div>\n          <fieldset className=\"space-y-4\">\n            <legend className=\"text-foreground text-sm leading-none font-medium\">\n              Etiquette\n            </legend>\n            <RadioGroup\n              className=\"flex gap-1.5\"\n              defaultValue={colorOptions[0]?.value}\n              value={color}\n              onValueChange={(value: EventColor) => setColor(value)}\n            >\n              {colorOptions.map((colorOption) => (\n                <RadioGroupItem\n                  key={colorOption.value}\n                  id={`color-${colorOption.value}`}\n                  value={colorOption.value}\n                  aria-label={colorOption.label}\n                  className={cn(\n                    \"size-6 shadow-none\",\n                    colorOption.bgClass,\n                    colorOption.borderClass,\n                  )}\n                />\n              ))}\n            </RadioGroup>\n          </fieldset>\n        </div>\n        <DialogFooter className=\"flex-row sm:justify-between\">\n          {event?.id && (\n            <Button\n              variant=\"outline\"\n              className=\"text-destructive hover:text-destructive\"\n              size=\"icon\"\n              onClick={handleDelete}\n              aria-label=\"Delete event\"\n            >\n              <RiDeleteBinLine size={16} aria-hidden=\"true\" />\n            </Button>\n          )}\n          <div className=\"flex flex-1 justify-end gap-2\">\n            <Button variant=\"outline\" onClick={onClose}>\n              Cancel\n            </Button>\n            <Button onClick={handleSave}>Save</Button>\n          </div>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  );\n}\n", "type": "registry:component"}, {"path": "components/event-calendar/event-item.tsx", "content": "\"use client\";\n\nimport { useMemo } from \"react\";\nimport type { DraggableAttributes } from \"@dnd-kit/core\";\nimport type { SyntheticListenerMap } from \"@dnd-kit/core/dist/hooks/utilities\";\nimport { differenceInMinutes, format, getMinutes, isPast } from \"date-fns\";\n\nimport {\n  getBorderRadiusClasses,\n  getEventColorClasses,\n  type CalendarEvent,\n} from \"@/components/event-calendar\";\nimport { cn } from \"@/lib/utils\";\n\n// Using date-fns format with custom formatting:\n// 'h' - hours (1-12)\n// 'a' - am/pm\n// ':mm' - minutes with leading zero (only if the token 'mm' is present)\nconst formatTimeWithOptionalMinutes = (date: Date) => {\n  return format(date, getMinutes(date) === 0 ? \"ha\" : \"h:mma\").toLowerCase();\n};\n\ninterface EventWrapperProps {\n  event: CalendarEvent;\n  isFirstDay?: boolean;\n  isLastDay?: boolean;\n  isDragging?: boolean;\n  onClick?: (e: React.MouseEvent) => void;\n  className?: string;\n  children: React.ReactNode;\n  currentTime?: Date;\n  dndListeners?: SyntheticListenerMap;\n  dndAttributes?: DraggableAttributes;\n  onMouseDown?: (e: React.MouseEvent) => void;\n  onTouchStart?: (e: React.TouchEvent) => void;\n}\n\n// Shared wrapper component for event styling\nfunction EventWrapper({\n  event,\n  isFirstDay = true,\n  isLastDay = true,\n  isDragging,\n  onClick,\n  className,\n  children,\n  currentTime,\n  dndListeners,\n  dndAttributes,\n  onMouseDown,\n  onTouchStart,\n}: EventWrapperProps) {\n  // Always use the currentTime (if provided) to determine if the event is in the past\n  const displayEnd = currentTime\n    ? new Date(\n        new Date(currentTime).getTime() +\n          (new Date(event.end).getTime() - new Date(event.start).getTime()),\n      )\n    : new Date(event.end);\n\n  const isEventInPast = isPast(displayEnd);\n\n  return (\n    <button\n      className={cn(\n        \"focus-visible:border-ring focus-visible:ring-ring/50 flex h-full w-full overflow-hidden px-1 text-left font-medium backdrop-blur-md transition outline-none select-none focus-visible:ring-[3px] data-dragging:cursor-grabbing data-dragging:shadow-lg data-past-event:line-through sm:px-2\",\n        getEventColorClasses(event.color),\n        getBorderRadiusClasses(isFirstDay, isLastDay),\n        className,\n      )}\n      data-dragging={isDragging || undefined}\n      data-past-event={isEventInPast || undefined}\n      onClick={onClick}\n      onMouseDown={onMouseDown}\n      onTouchStart={onTouchStart}\n      {...dndListeners}\n      {...dndAttributes}\n    >\n      {children}\n    </button>\n  );\n}\n\ninterface EventItemProps {\n  event: CalendarEvent;\n  view: \"month\" | \"week\" | \"day\" | \"agenda\";\n  isDragging?: boolean;\n  onClick?: (e: React.MouseEvent) => void;\n  showTime?: boolean;\n  currentTime?: Date; // For updating time during drag\n  isFirstDay?: boolean;\n  isLastDay?: boolean;\n  children?: React.ReactNode;\n  className?: string;\n  dndListeners?: SyntheticListenerMap;\n  dndAttributes?: DraggableAttributes;\n  onMouseDown?: (e: React.MouseEvent) => void;\n  onTouchStart?: (e: React.TouchEvent) => void;\n}\n\nexport function EventItem({\n  event,\n  view,\n  isDragging,\n  onClick,\n  showTime,\n  currentTime,\n  isFirstDay = true,\n  isLastDay = true,\n  children,\n  className,\n  dndListeners,\n  dndAttributes,\n  onMouseDown,\n  onTouchStart,\n}: EventItemProps) {\n  const eventColor = event.color;\n\n  // Use the provided currentTime (for dragging) or the event's actual time\n  const displayStart = useMemo(() => {\n    return currentTime || new Date(event.start);\n  }, [currentTime, event.start]);\n\n  const displayEnd = useMemo(() => {\n    return currentTime\n      ? new Date(\n          new Date(currentTime).getTime() +\n            (new Date(event.end).getTime() - new Date(event.start).getTime()),\n        )\n      : new Date(event.end);\n  }, [currentTime, event.start, event.end]);\n\n  // Calculate event duration in minutes\n  const durationMinutes = useMemo(() => {\n    return differenceInMinutes(displayEnd, displayStart);\n  }, [displayStart, displayEnd]);\n\n  const getEventTime = () => {\n    if (event.allDay) return \"All day\";\n\n    // For short events (less than 45 minutes), only show start time\n    if (durationMinutes < 45) {\n      return formatTimeWithOptionalMinutes(displayStart);\n    }\n\n    // For longer events, show both start and end time\n    return `${formatTimeWithOptionalMinutes(displayStart)} - ${formatTimeWithOptionalMinutes(displayEnd)}`;\n  };\n\n  if (view === \"month\") {\n    return (\n      <EventWrapper\n        event={event}\n        isFirstDay={isFirstDay}\n        isLastDay={isLastDay}\n        isDragging={isDragging}\n        onClick={onClick}\n        className={cn(\n          \"mt-[var(--event-gap)] h-[var(--event-height)] items-center text-[10px] sm:text-[13px]\",\n          className,\n        )}\n        currentTime={currentTime}\n        dndListeners={dndListeners}\n        dndAttributes={dndAttributes}\n        onMouseDown={onMouseDown}\n        onTouchStart={onTouchStart}\n      >\n        {children || (\n          <span className=\"truncate\">\n            {!event.allDay && (\n              <span className=\"truncate sm:text-xs font-normal opacity-70 uppercase\">\n                {formatTimeWithOptionalMinutes(displayStart)}{\" \"}\n              </span>\n            )}\n            {event.title}\n          </span>\n        )}\n      </EventWrapper>\n    );\n  }\n\n  if (view === \"week\" || view === \"day\") {\n    return (\n      <EventWrapper\n        event={event}\n        isFirstDay={isFirstDay}\n        isLastDay={isLastDay}\n        isDragging={isDragging}\n        onClick={onClick}\n        className={cn(\n          \"py-1\",\n          durationMinutes < 45 ? \"items-center\" : \"flex-col\",\n          view === \"week\" ? \"text-[10px] sm:text-[13px]\" : \"text-[13px]\",\n          className,\n        )}\n        currentTime={currentTime}\n        dndListeners={dndListeners}\n        dndAttributes={dndAttributes}\n        onMouseDown={onMouseDown}\n        onTouchStart={onTouchStart}\n      >\n        {durationMinutes < 45 ? (\n          <div className=\"truncate\">\n            {event.title}{\" \"}\n            {showTime && (\n              <span className=\"opacity-70\">\n                {formatTimeWithOptionalMinutes(displayStart)}\n              </span>\n            )}\n          </div>\n        ) : (\n          <>\n            <div className=\"truncate font-medium\">{event.title}</div>\n            {showTime && (\n              <div className=\"truncate font-normal opacity-70 sm:text-xs uppercase\">\n                {getEventTime()}\n              </div>\n            )}\n          </>\n        )}\n      </EventWrapper>\n    );\n  }\n\n  // Agenda view - kept separate since it's significantly different\n  return (\n    <button\n      className={cn(\n        \"focus-visible:border-ring focus-visible:ring-ring/50 flex w-full flex-col gap-1 rounded p-2 text-left transition outline-none focus-visible:ring-[3px] data-past-event:line-through data-past-event:opacity-90\",\n        getEventColorClasses(eventColor),\n        className,\n      )}\n      data-past-event={isPast(new Date(event.end)) || undefined}\n      onClick={onClick}\n      onMouseDown={onMouseDown}\n      onTouchStart={onTouchStart}\n      {...dndListeners}\n      {...dndAttributes}\n    >\n      <div className=\"text-sm font-medium\">{event.title}</div>\n      <div className=\"text-xs opacity-70\">\n        {event.allDay ? (\n          <span>All day</span>\n        ) : (\n          <span className=\"uppercase\">\n            {formatTimeWithOptionalMinutes(displayStart)} -{\" \"}\n            {formatTimeWithOptionalMinutes(displayEnd)}\n          </span>\n        )}\n        {event.location && (\n          <>\n            <span className=\"px-1 opacity-35\"> · </span>\n            <span>{event.location}</span>\n          </>\n        )}\n      </div>\n      {event.description && (\n        <div className=\"my-1 text-xs opacity-90\">{event.description}</div>\n      )}\n    </button>\n  );\n}\n", "type": "registry:component"}, {"path": "components/event-calendar/events-popup.tsx", "content": "\"use client\";\n\nimport { useEffect, useMemo, useRef } from \"react\";\nimport { format, isSameDay } from \"date-fns\";\nimport { XIcon } from \"lucide-react\";\n\nimport { EventItem, type CalendarEvent } from \"@/components/event-calendar\";\n\ninterface EventsPopupProps {\n  date: Date;\n  events: CalendarEvent[];\n  position: { top: number; left: number };\n  onClose: () => void;\n  onEventSelect: (event: CalendarEvent) => void;\n}\n\nexport function EventsPopup({\n  date,\n  events,\n  position,\n  onClose,\n  onEventSelect,\n}: EventsPopupProps) {\n  const popupRef = useRef<HTMLDivElement>(null);\n\n  // <PERSON>le click outside to close popup\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (\n        popupRef.current &&\n        !popupRef.current.contains(event.target as Node)\n      ) {\n        onClose();\n      }\n    };\n\n    document.addEventListener(\"mousedown\", handleClickOutside);\n    return () => {\n      document.removeEventListener(\"mousedown\", handleClickOutside);\n    };\n  }, [onClose]);\n\n  // Handle escape key to close popup\n  useEffect(() => {\n    const handleEscKey = (event: KeyboardEvent) => {\n      if (event.key === \"Escape\") {\n        onClose();\n      }\n    };\n\n    document.addEventListener(\"keydown\", handleEscKey);\n    return () => {\n      document.removeEventListener(\"keydown\", handleEscKey);\n    };\n  }, [onClose]);\n\n  const handleEventClick = (event: CalendarEvent) => {\n    onEventSelect(event);\n    onClose();\n  };\n\n  // Adjust position to ensure popup stays within viewport\n  const adjustedPosition = useMemo(() => {\n    const positionCopy = { ...position };\n\n    // Check if we need to adjust the position to fit in the viewport\n    if (popupRef.current) {\n      const rect = popupRef.current.getBoundingClientRect();\n      const viewportWidth = window.innerWidth;\n      const viewportHeight = window.innerHeight;\n\n      // Adjust horizontally if needed\n      if (positionCopy.left + rect.width > viewportWidth) {\n        positionCopy.left = Math.max(0, viewportWidth - rect.width);\n      }\n\n      // Adjust vertically if needed\n      if (positionCopy.top + rect.height > viewportHeight) {\n        positionCopy.top = Math.max(0, viewportHeight - rect.height);\n      }\n    }\n\n    return positionCopy;\n  }, [position]);\n\n  return (\n    <div\n      ref={popupRef}\n      className=\"bg-background absolute z-50 max-h-96 w-80 overflow-auto rounded-md border shadow-lg\"\n      style={{\n        top: `${adjustedPosition.top}px`,\n        left: `${adjustedPosition.left}px`,\n      }}\n    >\n      <div className=\"bg-background sticky top-0 flex items-center justify-between border-b p-3\">\n        <h3 className=\"font-medium\">{format(date, \"d MMMM yyyy\")}</h3>\n        <button\n          onClick={onClose}\n          className=\"hover:bg-muted rounded-full p-1\"\n          aria-label=\"Close\"\n        >\n          <XIcon className=\"h-4 w-4\" />\n        </button>\n      </div>\n\n      <div className=\"space-y-2 p-3\">\n        {events.length === 0 ? (\n          <div className=\"text-muted-foreground py-2 text-sm\">No events</div>\n        ) : (\n          events.map((event) => {\n            const eventStart = new Date(event.start);\n            const eventEnd = new Date(event.end);\n            const isFirstDay = isSameDay(date, eventStart);\n            const isLastDay = isSameDay(date, eventEnd);\n\n            return (\n              <div\n                key={event.id}\n                className=\"cursor-pointer\"\n                onClick={() => handleEventClick(event)}\n              >\n                <EventItem\n                  event={event}\n                  view=\"agenda\"\n                  isFirstDay={isFirstDay}\n                  isLastDay={isLastDay}\n                />\n              </div>\n            );\n          })\n        )}\n      </div>\n    </div>\n  );\n}\n", "type": "registry:component"}, {"path": "components/event-calendar/event-calendar.tsx", "content": "\"use client\";\n\nimport { useEffect, useMemo, useState } from \"react\";\nimport { useCalendarContext } from \"./calendar-context\";\nimport {\n  addDays,\n  addMonths,\n  addWeeks,\n  endOfWeek,\n  format,\n  isSameMonth,\n  startOfWeek,\n  subMonths,\n  subWeeks,\n} from \"date-fns\";\nimport {\n  ChevronDownIcon,\n  ChevronLeftIcon,\n  ChevronRightIcon,\n} from \"lucide-react\";\nimport { toast } from \"sonner\";\n\nimport {\n  addHoursToDate,\n  AgendaDaysToShow,\n  AgendaView,\n  CalendarDndProvider,\n  CalendarEvent,\n  CalendarView,\n  DayView,\n  EventDialog,\n  EventGap,\n  EventHeight,\n  MonthView,\n  WeekCellsHeight,\n  WeekView,\n} from \"@/components/event-calendar\";\nimport { cn } from \"@/lib/utils\";\nimport { Button } from \"@/components/ui/button\";\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuShortcut,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\";\nimport { SidebarTrigger, useSidebar } from \"@/components/ui/sidebar\";\nimport ThemeToggle from \"@/components/theme-toggle\";\nimport Participants from \"@/components/participants\";\n\nexport interface EventCalendarProps {\n  events?: CalendarEvent[];\n  onEventAdd?: (event: CalendarEvent) => void;\n  onEventUpdate?: (event: CalendarEvent) => void;\n  onEventDelete?: (eventId: string) => void;\n  className?: string;\n  initialView?: CalendarView;\n}\n\nexport function EventCalendar({\n  events = [],\n  onEventAdd,\n  onEventUpdate,\n  onEventDelete,\n  className,\n  initialView = \"month\",\n}: EventCalendarProps) {\n  // Use the shared calendar context instead of local state\n  const { currentDate, setCurrentDate } = useCalendarContext();\n  const [view, setView] = useState<CalendarView>(initialView);\n  const [isEventDialogOpen, setIsEventDialogOpen] = useState(false);\n  const [selectedEvent, setSelectedEvent] = useState<CalendarEvent | null>(\n    null,\n  );\n  const { open } = useSidebar();\n\n  // Add keyboard shortcuts for view switching\n  useEffect(() => {\n    const handleKeyDown = (e: KeyboardEvent) => {\n      // Skip if user is typing in an input, textarea or contentEditable element\n      // or if the event dialog is open\n      if (\n        isEventDialogOpen ||\n        e.target instanceof HTMLInputElement ||\n        e.target instanceof HTMLTextAreaElement ||\n        (e.target instanceof HTMLElement && e.target.isContentEditable)\n      ) {\n        return;\n      }\n\n      switch (e.key.toLowerCase()) {\n        case \"m\":\n          setView(\"month\");\n          break;\n        case \"w\":\n          setView(\"week\");\n          break;\n        case \"d\":\n          setView(\"day\");\n          break;\n        case \"a\":\n          setView(\"agenda\");\n          break;\n      }\n    };\n\n    window.addEventListener(\"keydown\", handleKeyDown);\n\n    return () => {\n      window.removeEventListener(\"keydown\", handleKeyDown);\n    };\n  }, [isEventDialogOpen]);\n\n  const handlePrevious = () => {\n    if (view === \"month\") {\n      setCurrentDate(subMonths(currentDate, 1));\n    } else if (view === \"week\") {\n      setCurrentDate(subWeeks(currentDate, 1));\n    } else if (view === \"day\") {\n      setCurrentDate(addDays(currentDate, -1));\n    } else if (view === \"agenda\") {\n      // For agenda view, go back 30 days (a full month)\n      setCurrentDate(addDays(currentDate, -AgendaDaysToShow));\n    }\n  };\n\n  const handleNext = () => {\n    if (view === \"month\") {\n      setCurrentDate(addMonths(currentDate, 1));\n    } else if (view === \"week\") {\n      setCurrentDate(addWeeks(currentDate, 1));\n    } else if (view === \"day\") {\n      setCurrentDate(addDays(currentDate, 1));\n    } else if (view === \"agenda\") {\n      // For agenda view, go forward 30 days (a full month)\n      setCurrentDate(addDays(currentDate, AgendaDaysToShow));\n    }\n  };\n\n  const handleToday = () => {\n    setCurrentDate(new Date());\n  };\n\n  const handleEventSelect = (event: CalendarEvent) => {\n    console.log(\"Event selected:\", event); // Debug log\n    setSelectedEvent(event);\n    setIsEventDialogOpen(true);\n  };\n\n  const handleEventCreate = (startTime: Date) => {\n    console.log(\"Creating new event at:\", startTime); // Debug log\n\n    // Snap to 15-minute intervals\n    const minutes = startTime.getMinutes();\n    const remainder = minutes % 15;\n    if (remainder !== 0) {\n      if (remainder < 7.5) {\n        // Round down to nearest 15 min\n        startTime.setMinutes(minutes - remainder);\n      } else {\n        // Round up to nearest 15 min\n        startTime.setMinutes(minutes + (15 - remainder));\n      }\n      startTime.setSeconds(0);\n      startTime.setMilliseconds(0);\n    }\n\n    const newEvent: CalendarEvent = {\n      id: \"\",\n      title: \"\",\n      start: startTime,\n      end: addHoursToDate(startTime, 1),\n      allDay: false,\n    };\n    setSelectedEvent(newEvent);\n    setIsEventDialogOpen(true);\n  };\n\n  const handleEventSave = (event: CalendarEvent) => {\n    if (event.id) {\n      onEventUpdate?.(event);\n      // Show toast notification when an event is updated\n      toast(`Event \"${event.title}\" updated`, {\n        description: format(new Date(event.start), \"MMM d, yyyy\"),\n        position: \"bottom-left\",\n      });\n    } else {\n      onEventAdd?.({\n        ...event,\n        id: Math.random().toString(36).substring(2, 11),\n      });\n      // Show toast notification when an event is added\n      toast(`Event \"${event.title}\" added`, {\n        description: format(new Date(event.start), \"MMM d, yyyy\"),\n        position: \"bottom-left\",\n      });\n    }\n    setIsEventDialogOpen(false);\n    setSelectedEvent(null);\n  };\n\n  const handleEventDelete = (eventId: string) => {\n    const deletedEvent = events.find((e) => e.id === eventId);\n    onEventDelete?.(eventId);\n    setIsEventDialogOpen(false);\n    setSelectedEvent(null);\n\n    // Show toast notification when an event is deleted\n    if (deletedEvent) {\n      toast(`Event \"${deletedEvent.title}\" deleted`, {\n        description: format(new Date(deletedEvent.start), \"MMM d, yyyy\"),\n        position: \"bottom-left\",\n      });\n    }\n  };\n\n  const handleEventUpdate = (updatedEvent: CalendarEvent) => {\n    onEventUpdate?.(updatedEvent);\n\n    // Show toast notification when an event is updated via drag and drop\n    toast(`Event \"${updatedEvent.title}\" moved`, {\n      description: format(new Date(updatedEvent.start), \"MMM d, yyyy\"),\n      position: \"bottom-left\",\n    });\n  };\n\n  const viewTitle = useMemo(() => {\n    if (view === \"month\") {\n      return format(currentDate, \"MMMM yyyy\");\n    } else if (view === \"week\") {\n      const start = startOfWeek(currentDate, { weekStartsOn: 0 });\n      const end = endOfWeek(currentDate, { weekStartsOn: 0 });\n      if (isSameMonth(start, end)) {\n        return format(start, \"MMMM yyyy\");\n      } else {\n        return `${format(start, \"MMM\")} - ${format(end, \"MMM yyyy\")}`;\n      }\n    } else if (view === \"day\") {\n      return (\n        <>\n          <span className=\"min-sm:hidden\" aria-hidden=\"true\">\n            {format(currentDate, \"MMM d, yyyy\")}\n          </span>\n          <span className=\"max-sm:hidden min-md:hidden\" aria-hidden=\"true\">\n            {format(currentDate, \"MMMM d, yyyy\")}\n          </span>\n          <span className=\"max-md:hidden\">\n            {format(currentDate, \"EEE MMMM d, yyyy\")}\n          </span>\n        </>\n      );\n    } else if (view === \"agenda\") {\n      // Show the month range for agenda view\n      const start = currentDate;\n      const end = addDays(currentDate, AgendaDaysToShow - 1);\n\n      if (isSameMonth(start, end)) {\n        return format(start, \"MMMM yyyy\");\n      } else {\n        return `${format(start, \"MMM\")} - ${format(end, \"MMM yyyy\")}`;\n      }\n    } else {\n      return format(currentDate, \"MMMM yyyy\");\n    }\n  }, [currentDate, view]);\n\n  return (\n    <div\n      className=\"flex has-data-[slot=month-view]:flex-1 flex-col rounded-lg\"\n      style={\n        {\n          \"--event-height\": `${EventHeight}px`,\n          \"--event-gap\": `${EventGap}px`,\n          \"--week-cells-height\": `${WeekCellsHeight}px`,\n        } as React.CSSProperties\n      }\n    >\n      <CalendarDndProvider onEventUpdate={handleEventUpdate}>\n        <div\n          className={cn(\n            \"flex flex-col sm:flex-row sm:items-center justify-between gap-2 py-5 sm:px-4\",\n            className,\n          )}\n        >\n          <div className=\"flex sm:flex-col max-sm:items-center justify-between gap-1.5\">\n            <div className=\"flex items-center gap-1.5\">\n              <SidebarTrigger\n                data-state={open ? \"invisible\" : \"visible\"}\n                className=\"peer size-7 text-muted-foreground/80 hover:text-foreground/80 hover:bg-transparent! sm:-ms-1.5 lg:data-[state=invisible]:opacity-0 lg:data-[state=invisible]:pointer-events-none transition-opacity ease-in-out duration-200\"\n                isOutsideSidebar\n              />\n              <h2 className=\"font-semibold text-xl lg:peer-data-[state=invisible]:-translate-x-7.5 transition-transform ease-in-out duration-300\">\n                {viewTitle}\n              </h2>\n            </div>\n            <Participants />\n          </div>\n          <div className=\"flex items-center justify-between gap-2\">\n            <div className=\"flex items-center justify-between gap-2\">\n              <div className=\"flex items-center sm:gap-2 max-sm:order-1\">\n                <Button\n                  variant=\"ghost\"\n                  size=\"icon\"\n                  className=\"max-sm:size-8\"\n                  onClick={handlePrevious}\n                  aria-label=\"Previous\"\n                >\n                  <ChevronLeftIcon size={16} aria-hidden=\"true\" />\n                </Button>\n                <Button\n                  variant=\"ghost\"\n                  size=\"icon\"\n                  className=\"max-sm:size-8\"\n                  onClick={handleNext}\n                  aria-label=\"Next\"\n                >\n                  <ChevronRightIcon size={16} aria-hidden=\"true\" />\n                </Button>\n              </div>\n              <Button\n                className=\"max-sm:h-8 max-sm:px-2.5!\"\n                onClick={handleToday}\n              >\n                Today\n              </Button>\n            </div>\n            <div className=\"flex items-center justify-between gap-2\">\n              <Button\n                variant=\"outline\"\n                className=\"max-sm:h-8 max-sm:px-2.5!\"\n                onClick={() => {\n                  setSelectedEvent(null); // Ensure we're creating a new event\n                  setIsEventDialogOpen(true);\n                }}\n              >\n                New Event\n              </Button>\n              <DropdownMenu>\n                <DropdownMenuTrigger asChild>\n                  <Button\n                    variant=\"outline\"\n                    className=\"gap-1.5 max-sm:h-8 max-sm:px-2! max-sm:gap-1\"\n                  >\n                    <span className=\"capitalize\">{view}</span>\n                    <ChevronDownIcon\n                      className=\"-me-1 opacity-60\"\n                      size={16}\n                      aria-hidden=\"true\"\n                    />\n                  </Button>\n                </DropdownMenuTrigger>\n                <DropdownMenuContent align=\"end\" className=\"min-w-32\">\n                  <DropdownMenuItem onClick={() => setView(\"month\")}>\n                    Month <DropdownMenuShortcut>M</DropdownMenuShortcut>\n                  </DropdownMenuItem>\n                  <DropdownMenuItem onClick={() => setView(\"week\")}>\n                    Week <DropdownMenuShortcut>W</DropdownMenuShortcut>\n                  </DropdownMenuItem>\n                  <DropdownMenuItem onClick={() => setView(\"day\")}>\n                    Day <DropdownMenuShortcut>D</DropdownMenuShortcut>\n                  </DropdownMenuItem>\n                  <DropdownMenuItem onClick={() => setView(\"agenda\")}>\n                    Agenda <DropdownMenuShortcut>A</DropdownMenuShortcut>\n                  </DropdownMenuItem>\n                </DropdownMenuContent>\n              </DropdownMenu>\n              <ThemeToggle />\n            </div>\n          </div>\n        </div>\n\n        <div className=\"flex flex-1 flex-col\">\n          {view === \"month\" && (\n            <MonthView\n              currentDate={currentDate}\n              events={events}\n              onEventSelect={handleEventSelect}\n              onEventCreate={handleEventCreate}\n            />\n          )}\n          {view === \"week\" && (\n            <WeekView\n              currentDate={currentDate}\n              events={events}\n              onEventSelect={handleEventSelect}\n              onEventCreate={handleEventCreate}\n            />\n          )}\n          {view === \"day\" && (\n            <DayView\n              currentDate={currentDate}\n              events={events}\n              onEventSelect={handleEventSelect}\n              onEventCreate={handleEventCreate}\n            />\n          )}\n          {view === \"agenda\" && (\n            <AgendaView\n              currentDate={currentDate}\n              events={events}\n              onEventSelect={handleEventSelect}\n            />\n          )}\n        </div>\n\n        <EventDialog\n          event={selectedEvent}\n          isOpen={isEventDialogOpen}\n          onClose={() => {\n            setIsEventDialogOpen(false);\n            setSelectedEvent(null);\n          }}\n          onSave={handleEventSave}\n          onDelete={handleEventDelete}\n        />\n      </CalendarDndProvider>\n    </div>\n  );\n}\n", "type": "registry:component"}, {"path": "components/event-calendar/index.ts", "content": "\"use client\";\n\n// Component exports\nexport { AgendaView } from \"./agenda-view\";\nexport { DayView } from \"./day-view\";\nexport { DraggableEvent } from \"./draggable-event\";\nexport { DroppableCell } from \"./droppable-cell\";\nexport { EventDialog } from \"./event-dialog\";\nexport { EventItem } from \"./event-item\";\nexport { EventsPopup } from \"./events-popup\";\nexport { EventCalendar } from \"./event-calendar\";\nexport { MonthView } from \"./month-view\";\nexport { WeekView } from \"./week-view\";\nexport { CalendarDndProvider, useCalendarDnd } from \"./calendar-dnd-context\";\n\n// Constants and utility exports\nexport * from \"./constants\";\nexport * from \"./utils\";\n\n// Hook exports\nexport * from \"./hooks/use-current-time-indicator\";\nexport * from \"./hooks/use-event-visibility\";\n\n// Type exports\nexport type { CalendarEvent, CalendarView, EventColor } from \"./types\";\n", "type": "registry:component"}, {"path": "components/event-calendar/month-view.tsx", "content": "\"use client\";\n\nimport React, { useEffect, useMemo, useState } from \"react\";\nimport {\n  addDays,\n  eachDayOfInterval,\n  endOfMonth,\n  endOfWeek,\n  format,\n  isSameDay,\n  isSameMonth,\n  isToday,\n  startOfMonth,\n  startOfWeek,\n} from \"date-fns\";\n\nimport {\n  Popover,\n  PopoverContent,\n  PopoverTrigger,\n} from \"@/components/ui/popover\";\nimport {\n  DraggableEvent,\n  DroppableCell,\n  EventGap,\n  EventHeight,\n  EventItem,\n  getAllEventsForDay,\n  getEventsForDay,\n  getSpanningEventsForDay,\n  sortEvents,\n  useEventVisibility,\n  type CalendarEvent,\n} from \"@/components/event-calendar\";\nimport { DefaultStartHour } from \"@/components/event-calendar/constants\";\n\ninterface MonthViewProps {\n  currentDate: Date;\n  events: CalendarEvent[];\n  onEventSelect: (event: CalendarEvent) => void;\n  onEventCreate: (startTime: Date) => void;\n}\n\nexport function MonthView({\n  currentDate,\n  events,\n  onEventSelect,\n  onEventCreate,\n}: MonthViewProps) {\n  const days = useMemo(() => {\n    const monthStart = startOfMonth(currentDate);\n    const monthEnd = endOfMonth(monthStart);\n    const calendarStart = startOfWeek(monthStart, { weekStartsOn: 0 });\n    const calendarEnd = endOfWeek(monthEnd, { weekStartsOn: 0 });\n\n    return eachDayOfInterval({ start: calendarStart, end: calendarEnd });\n  }, [currentDate]);\n\n  const weekdays = useMemo(() => {\n    return Array.from({ length: 7 }).map((_, i) => {\n      const date = addDays(startOfWeek(new Date()), i);\n      return format(date, \"EEE\");\n    });\n  }, []);\n\n  const weeks = useMemo(() => {\n    const result = [];\n    let week = [];\n\n    for (let i = 0; i < days.length; i++) {\n      week.push(days[i]);\n      if (week.length === 7 || i === days.length - 1) {\n        result.push(week);\n        week = [];\n      }\n    }\n\n    return result;\n  }, [days]);\n\n  const handleEventClick = (event: CalendarEvent, e: React.MouseEvent) => {\n    e.stopPropagation();\n    onEventSelect(event);\n  };\n\n  const [isMounted, setIsMounted] = useState(false);\n  const { contentRef, getVisibleEventCount } = useEventVisibility({\n    eventHeight: EventHeight,\n    eventGap: EventGap,\n  });\n\n  useEffect(() => {\n    setIsMounted(true);\n  }, []);\n\n  return (\n    <div data-slot=\"month-view\" className=\"contents\">\n      <div className=\"border-border/70 grid grid-cols-7 border-y uppercase\">\n        {weekdays.map((day) => (\n          <div\n            key={day}\n            className=\"text-muted-foreground/70 py-2 text-center text-xs\"\n          >\n            {day}\n          </div>\n        ))}\n      </div>\n      <div className=\"grid flex-1 auto-rows-fr\">\n        {weeks.map((week, weekIndex) => (\n          <div\n            key={`week-${weekIndex}`}\n            className=\"grid grid-cols-7 [&:last-child>*]:border-b-0\"\n          >\n            {week.map((day, dayIndex) => {\n              if (!day) return null; // Skip if day is undefined\n\n              const dayEvents = getEventsForDay(events, day);\n              const spanningEvents = getSpanningEventsForDay(events, day);\n              const isCurrentMonth = isSameMonth(day, currentDate);\n              const cellId = `month-cell-${day.toISOString()}`;\n              const allDayEvents = [...spanningEvents, ...dayEvents];\n              const allEvents = getAllEventsForDay(events, day);\n\n              const isReferenceCell = weekIndex === 0 && dayIndex === 0;\n              const visibleCount = isMounted\n                ? getVisibleEventCount(allDayEvents.length)\n                : undefined;\n              const hasMore =\n                visibleCount !== undefined &&\n                allDayEvents.length > visibleCount;\n              const remainingCount = hasMore\n                ? allDayEvents.length - visibleCount\n                : 0;\n\n              return (\n                <div\n                  key={day.toString()}\n                  className=\"group border-border/70 data-outside-cell:bg-muted/25 data-outside-cell:text-muted-foreground/70 border-r border-b last:border-r-0\"\n                  data-today={isToday(day) || undefined}\n                  data-outside-cell={!isCurrentMonth || undefined}\n                >\n                  <DroppableCell\n                    id={cellId}\n                    date={day}\n                    onClick={() => {\n                      const startTime = new Date(day);\n                      startTime.setHours(DefaultStartHour, 0, 0);\n                      onEventCreate(startTime);\n                    }}\n                  >\n                    <div className=\"group-data-today:bg-primary group-data-today:text-primary-foreground mt-1 inline-flex size-6 items-center justify-center rounded-full text-sm\">\n                      {format(day, \"d\")}\n                    </div>\n                    <div\n                      ref={isReferenceCell ? contentRef : null}\n                      className=\"min-h-[calc((var(--event-height)+var(--event-gap))*2)] sm:min-h-[calc((var(--event-height)+var(--event-gap))*3)] lg:min-h-[calc((var(--event-height)+var(--event-gap))*4)]\"\n                    >\n                      {sortEvents(allDayEvents).map((event, index) => {\n                        const eventStart = new Date(event.start);\n                        const eventEnd = new Date(event.end);\n                        const isFirstDay = isSameDay(day, eventStart);\n                        const isLastDay = isSameDay(day, eventEnd);\n\n                        const isHidden =\n                          isMounted && visibleCount && index >= visibleCount;\n\n                        if (!visibleCount) return null;\n\n                        if (!isFirstDay) {\n                          return (\n                            <div\n                              key={`spanning-${event.id}-${day.toISOString().slice(0, 10)}`}\n                              className=\"aria-hidden:hidden\"\n                              aria-hidden={isHidden ? \"true\" : undefined}\n                            >\n                              <EventItem\n                                onClick={(e) => handleEventClick(event, e)}\n                                event={event}\n                                view=\"month\"\n                                isFirstDay={isFirstDay}\n                                isLastDay={isLastDay}\n                              >\n                                <div className=\"invisible\" aria-hidden={true}>\n                                  {!event.allDay && (\n                                    <span>\n                                      {format(\n                                        new Date(event.start),\n                                        \"h:mm\",\n                                      )}{\" \"}\n                                    </span>\n                                  )}\n                                  {event.title}\n                                </div>\n                              </EventItem>\n                            </div>\n                          );\n                        }\n\n                        return (\n                          <div\n                            key={event.id}\n                            className=\"aria-hidden:hidden\"\n                            aria-hidden={isHidden ? \"true\" : undefined}\n                          >\n                            <DraggableEvent\n                              event={event}\n                              view=\"month\"\n                              onClick={(e) => handleEventClick(event, e)}\n                              isFirstDay={isFirstDay}\n                              isLastDay={isLastDay}\n                            />\n                          </div>\n                        );\n                      })}\n\n                      {hasMore && (\n                        <Popover modal>\n                          <PopoverTrigger asChild>\n                            <button\n                              className=\"focus-visible:border-ring focus-visible:ring-ring/50 text-muted-foreground hover:text-foreground hover:bg-muted/50 mt-[var(--event-gap)] flex h-[var(--event-height)] w-full items-center overflow-hidden px-1 text-left text-[10px] backdrop-blur-md transition outline-none select-none focus-visible:ring-[3px] sm:px-2 sm:text-xs\"\n                              onClick={(e) => e.stopPropagation()}\n                            >\n                              <span>\n                                + {remainingCount}{\" \"}\n                                <span className=\"max-sm:sr-only\">more</span>\n                              </span>\n                            </button>\n                          </PopoverTrigger>\n                          <PopoverContent\n                            align=\"center\"\n                            className=\"max-w-52 p-3\"\n                            style={\n                              {\n                                \"--event-height\": `${EventHeight}px`,\n                              } as React.CSSProperties\n                            }\n                          >\n                            <div className=\"space-y-2\">\n                              <div className=\"text-sm font-medium\">\n                                {format(day, \"EEE d\")}\n                              </div>\n                              <div className=\"space-y-1\">\n                                {sortEvents(allEvents).map((event) => {\n                                  const eventStart = new Date(event.start);\n                                  const eventEnd = new Date(event.end);\n                                  const isFirstDay = isSameDay(day, eventStart);\n                                  const isLastDay = isSameDay(day, eventEnd);\n\n                                  return (\n                                    <EventItem\n                                      key={event.id}\n                                      onClick={(e) =>\n                                        handleEventClick(event, e)\n                                      }\n                                      event={event}\n                                      view=\"month\"\n                                      isFirstDay={isFirstDay}\n                                      isLastDay={isLastDay}\n                                    />\n                                  );\n                                })}\n                              </div>\n                            </div>\n                          </PopoverContent>\n                        </Popover>\n                      )}\n                    </div>\n                  </DroppableCell>\n                </div>\n              );\n            })}\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n}\n", "type": "registry:component"}, {"path": "components/event-calendar/types.ts", "content": "export type CalendarView = \"month\" | \"week\" | \"day\" | \"agenda\";\n\nexport interface CalendarEvent {\n  id: string;\n  title: string;\n  description?: string;\n  start: Date;\n  end: Date;\n  allDay?: boolean;\n  color?: EventColor;\n  label?: string;\n  location?: string;\n}\n\nexport type EventColor = \"blue\" | \"orange\" | \"violet\" | \"rose\" | \"emerald\";\n", "type": "registry:component"}, {"path": "components/event-calendar/utils.ts", "content": "import { isSameDay } from \"date-fns\";\n\nimport type { CalendarEvent, EventColor } from \"@/components/event-calendar\";\n\n/**\n * Get CSS classes for event colors\n */\nexport function getEventColorClasses(color?: EventColor | string): string {\n  const eventColor = color || \"sky\";\n\n  switch (eventColor) {\n    case \"sky\":\n      return \"bg-blue-200/50 hover:bg-blue-200/40 text-blue-900/90 dark:bg-blue-400/25 dark:hover:bg-blue-400/20 dark:text-blue-200 shadow-blue-700/8\";\n    case \"violet\":\n      return \"bg-violet-200/50 hover:bg-violet-200/40 text-violet-900/90 dark:bg-violet-400/25 dark:hover:bg-violet-400/20 dark:text-violet-200 shadow-violet-700/8\";\n    case \"rose\":\n      return \"bg-rose-200/50 hover:bg-rose-200/40 text-rose-900/90 dark:bg-rose-400/25 dark:hover:bg-rose-400/20 dark:text-rose-200 shadow-rose-700/8\";\n    case \"emerald\":\n      return \"bg-emerald-200/50 hover:bg-emerald-200/40 text-emerald-900/90 dark:bg-emerald-400/25 dark:hover:bg-emerald-400/20 dark:text-emerald-200 shadow-emerald-700/8\";\n    case \"orange\":\n      return \"bg-orange-200/50 hover:bg-orange-200/40 text-orange-900/90 dark:bg-orange-400/25 dark:hover:bg-orange-400/20 dark:text-orange-200 shadow-orange-700/8\";\n    default:\n      return \"bg-blue-200/50 hover:bg-blue-200/40 text-blue-900/90 dark:bg-blue-400/25 dark:hover:bg-blue-400/20 dark:text-blue-200 shadow-blue-700/8\";\n  }\n}\n\n/**\n * Get CSS classes for border radius based on event position in multi-day events\n */\nexport function getBorderRadiusClasses(\n  isFirstDay: boolean,\n  isLastDay: boolean,\n): string {\n  if (isFirstDay && isLastDay) {\n    return \"rounded\"; // Both ends rounded\n  } else if (isFirstDay) {\n    return \"rounded-l rounded-r-none not-in-data-[slot=popover-content]:w-[calc(100%+5px)]\"; // Only left end rounded\n  } else if (isLastDay) {\n    return \"rounded-r rounded-l-none not-in-data-[slot=popover-content]:w-[calc(100%+4px)] not-in-data-[slot=popover-content]:-translate-x-[4px]\"; // Only right end rounded\n  } else {\n    return \"rounded-none not-in-data-[slot=popover-content]:w-[calc(100%+9px)] not-in-data-[slot=popover-content]:-translate-x-[4px]\"; // No rounded corners\n  }\n}\n\n/**\n * Check if an event is a multi-day event\n */\nexport function isMultiDayEvent(event: CalendarEvent): boolean {\n  const eventStart = new Date(event.start);\n  const eventEnd = new Date(event.end);\n  return event.allDay || eventStart.getDate() !== eventEnd.getDate();\n}\n\n/**\n * Filter events for a specific day\n */\nexport function getEventsForDay(\n  events: CalendarEvent[],\n  day: Date,\n): CalendarEvent[] {\n  return events\n    .filter((event) => {\n      const eventStart = new Date(event.start);\n      return isSameDay(day, eventStart);\n    })\n    .sort((a, b) => new Date(a.start).getTime() - new Date(b.start).getTime());\n}\n\n/**\n * Sort events with multi-day events first, then by start time\n */\nexport function sortEvents(events: CalendarEvent[]): CalendarEvent[] {\n  return [...events].sort((a, b) => {\n    const aIsMultiDay = isMultiDayEvent(a);\n    const bIsMultiDay = isMultiDayEvent(b);\n\n    if (aIsMultiDay && !bIsMultiDay) return -1;\n    if (!aIsMultiDay && bIsMultiDay) return 1;\n\n    return new Date(a.start).getTime() - new Date(b.start).getTime();\n  });\n}\n\n/**\n * Get multi-day events that span across a specific day (but don't start on that day)\n */\nexport function getSpanningEventsForDay(\n  events: CalendarEvent[],\n  day: Date,\n): CalendarEvent[] {\n  return events.filter((event) => {\n    if (!isMultiDayEvent(event)) return false;\n\n    const eventStart = new Date(event.start);\n    const eventEnd = new Date(event.end);\n\n    // Only include if it's not the start day but is either the end day or a middle day\n    return (\n      !isSameDay(day, eventStart) &&\n      (isSameDay(day, eventEnd) || (day > eventStart && day < eventEnd))\n    );\n  });\n}\n\n/**\n * Get all events visible on a specific day (starting, ending, or spanning)\n */\nexport function getAllEventsForDay(\n  events: CalendarEvent[],\n  day: Date,\n): CalendarEvent[] {\n  return events.filter((event) => {\n    const eventStart = new Date(event.start);\n    const eventEnd = new Date(event.end);\n    return (\n      isSameDay(day, eventStart) ||\n      isSameDay(day, eventEnd) ||\n      (day > eventStart && day < eventEnd)\n    );\n  });\n}\n\n/**\n * Get all events for a day (for agenda view)\n */\nexport function getAgendaEventsForDay(\n  events: CalendarEvent[],\n  day: Date,\n): CalendarEvent[] {\n  return events\n    .filter((event) => {\n      const eventStart = new Date(event.start);\n      const eventEnd = new Date(event.end);\n      return (\n        isSameDay(day, eventStart) ||\n        isSameDay(day, eventEnd) ||\n        (day > eventStart && day < eventEnd)\n      );\n    })\n    .sort((a, b) => new Date(a.start).getTime() - new Date(b.start).getTime());\n}\n\n/**\n * Add hours to a date\n */\nexport function addHoursToDate(date: Date, hours: number): Date {\n  const result = new Date(date);\n  result.setHours(result.getHours() + hours);\n  return result;\n}\n", "type": "registry:component"}, {"path": "components/event-calendar/week-view.tsx", "content": "\"use client\";\n\nimport React, { useMemo } from \"react\";\nimport {\n  addHours,\n  areIntervalsOverlapping,\n  differenceInMinutes,\n  eachDayOfInterval,\n  eachHourOfInterval,\n  endOfWeek,\n  format,\n  getHours,\n  getMinutes,\n  isBefore,\n  isSameDay,\n  isToday,\n  startOfDay,\n  startOfWeek,\n} from \"date-fns\";\n\nimport {\n  DraggableEvent,\n  DroppableCell,\n  EventItem,\n  isMultiDayEvent,\n  useCurrentTimeIndicator,\n  WeekCellsHeight,\n  type CalendarEvent,\n} from \"@/components/event-calendar\";\nimport { StartHour, EndHour } from \"@/components/event-calendar/constants\";\nimport { cn } from \"@/lib/utils\";\n\ninterface WeekViewProps {\n  currentDate: Date;\n  events: CalendarEvent[];\n  onEventSelect: (event: CalendarEvent) => void;\n  onEventCreate: (startTime: Date) => void;\n}\n\ninterface PositionedEvent {\n  event: CalendarEvent;\n  top: number;\n  height: number;\n  left: number;\n  width: number;\n  zIndex: number;\n}\n\nexport function WeekView({\n  currentDate,\n  events,\n  onEventSelect,\n  onEventCreate,\n}: WeekViewProps) {\n  const days = useMemo(() => {\n    const weekStart = startOfWeek(currentDate, { weekStartsOn: 0 });\n    const weekEnd = endOfWeek(currentDate, { weekStartsOn: 0 });\n    return eachDayOfInterval({ start: weekStart, end: weekEnd });\n  }, [currentDate]);\n\n  const weekStart = useMemo(\n    () => startOfWeek(currentDate, { weekStartsOn: 0 }),\n    [currentDate],\n  );\n\n  const hours = useMemo(() => {\n    const dayStart = startOfDay(currentDate);\n    return eachHourOfInterval({\n      start: addHours(dayStart, StartHour),\n      end: addHours(dayStart, EndHour - 1),\n    });\n  }, [currentDate]);\n\n  // Get all-day events and multi-day events for the week\n  const allDayEvents = useMemo(() => {\n    return events\n      .filter((event) => {\n        // Include explicitly marked all-day events or multi-day events\n        return event.allDay || isMultiDayEvent(event);\n      })\n      .filter((event) => {\n        const eventStart = new Date(event.start);\n        const eventEnd = new Date(event.end);\n        return days.some(\n          (day) =>\n            isSameDay(day, eventStart) ||\n            isSameDay(day, eventEnd) ||\n            (day > eventStart && day < eventEnd),\n        );\n      });\n  }, [events, days]);\n\n  // Process events for each day to calculate positions\n  const processedDayEvents = useMemo(() => {\n    const result = days.map((day) => {\n      // Get events for this day that are not all-day events or multi-day events\n      const dayEvents = events.filter((event) => {\n        // Skip all-day events and multi-day events\n        if (event.allDay || isMultiDayEvent(event)) return false;\n\n        const eventStart = new Date(event.start);\n        const eventEnd = new Date(event.end);\n\n        // Check if event is on this day\n        return (\n          isSameDay(day, eventStart) ||\n          isSameDay(day, eventEnd) ||\n          (eventStart < day && eventEnd > day)\n        );\n      });\n\n      // Sort events by start time and duration\n      const sortedEvents = [...dayEvents].sort((a, b) => {\n        const aStart = new Date(a.start);\n        const bStart = new Date(b.start);\n        const aEnd = new Date(a.end);\n        const bEnd = new Date(b.end);\n\n        // First sort by start time\n        if (aStart < bStart) return -1;\n        if (aStart > bStart) return 1;\n\n        // If start times are equal, sort by duration (longer events first)\n        const aDuration = differenceInMinutes(aEnd, aStart);\n        const bDuration = differenceInMinutes(bEnd, bStart);\n        return bDuration - aDuration;\n      });\n\n      // Calculate positions for each event\n      const positionedEvents: PositionedEvent[] = [];\n      const dayStart = startOfDay(day);\n\n      // Track columns for overlapping events\n      const columns: { event: CalendarEvent; end: Date }[][] = [];\n\n      sortedEvents.forEach((event) => {\n        const eventStart = new Date(event.start);\n        const eventEnd = new Date(event.end);\n\n        // Adjust start and end times if they're outside this day\n        const adjustedStart = isSameDay(day, eventStart)\n          ? eventStart\n          : dayStart;\n        const adjustedEnd = isSameDay(day, eventEnd)\n          ? eventEnd\n          : addHours(dayStart, 24);\n\n        // Calculate top position and height\n        const startHour =\n          getHours(adjustedStart) + getMinutes(adjustedStart) / 60;\n        const endHour = getHours(adjustedEnd) + getMinutes(adjustedEnd) / 60;\n\n        // Adjust the top calculation to account for the new start time\n        const top = (startHour - StartHour) * WeekCellsHeight;\n        const height = (endHour - startHour) * WeekCellsHeight;\n\n        // Find a column for this event\n        let columnIndex = 0;\n        let placed = false;\n\n        while (!placed) {\n          const col = columns[columnIndex] || [];\n          if (col.length === 0) {\n            columns[columnIndex] = col;\n            placed = true;\n          } else {\n            const overlaps = col.some((c) =>\n              areIntervalsOverlapping(\n                { start: adjustedStart, end: adjustedEnd },\n                {\n                  start: new Date(c.event.start),\n                  end: new Date(c.event.end),\n                },\n              ),\n            );\n            if (!overlaps) {\n              placed = true;\n            } else {\n              columnIndex++;\n            }\n          }\n        }\n\n        // Ensure column is initialized before pushing\n        const currentColumn = columns[columnIndex] || [];\n        columns[columnIndex] = currentColumn;\n        currentColumn.push({ event, end: adjustedEnd });\n\n        // Calculate width and left position based on number of columns\n        const width = columnIndex === 0 ? 1 : 0.9;\n        const left = columnIndex === 0 ? 0 : columnIndex * 0.1;\n\n        positionedEvents.push({\n          event,\n          top,\n          height,\n          left,\n          width,\n          zIndex: 10 + columnIndex, // Higher columns get higher z-index\n        });\n      });\n\n      return positionedEvents;\n    });\n\n    return result;\n  }, [days, events]);\n\n  const handleEventClick = (event: CalendarEvent, e: React.MouseEvent) => {\n    e.stopPropagation();\n    onEventSelect(event);\n  };\n\n  const showAllDaySection = allDayEvents.length > 0;\n  const { currentTimePosition, currentTimeVisible } = useCurrentTimeIndicator(\n    currentDate,\n    \"week\",\n  );\n\n  return (\n    <div data-slot=\"week-view\" className=\"flex h-full flex-col\">\n      <div className=\"bg-background/80 border-border/70 sticky top-0 z-30 grid grid-cols-8 border-y backdrop-blur-md uppercase\">\n        <div className=\"text-muted-foreground/70 py-2 text-center text-xs\">\n          <span className=\"max-[479px]:sr-only\">{format(new Date(), \"O\")}</span>\n        </div>\n        {days.map((day) => (\n          <div\n            key={day.toString()}\n            className=\"data-today:text-foreground text-muted-foreground/70 py-2 text-center text-xs data-today:font-medium\"\n            data-today={isToday(day) || undefined}\n          >\n            <span className=\"sm:hidden\" aria-hidden=\"true\">\n              {format(day, \"E\")[0]} {format(day, \"d\")}\n            </span>\n            <span className=\"max-sm:hidden\">{format(day, \"EEE dd\")}</span>\n          </div>\n        ))}\n      </div>\n\n      {showAllDaySection && (\n        <div className=\"border-border/70 bg-muted/50 border-b\">\n          <div className=\"grid grid-cols-8\">\n            <div className=\"border-border/70 relative border-r\">\n              <span className=\"text-muted-foreground/70 absolute bottom-0 left-0 h-6 w-16 max-w-full pe-2 text-right text-[10px] sm:pe-4 sm:text-xs\">\n                All day\n              </span>\n            </div>\n            {days.map((day, dayIndex) => {\n              const dayAllDayEvents = allDayEvents.filter((event) => {\n                const eventStart = new Date(event.start);\n                const eventEnd = new Date(event.end);\n                return (\n                  isSameDay(day, eventStart) ||\n                  (day > eventStart && day < eventEnd) ||\n                  isSameDay(day, eventEnd)\n                );\n              });\n\n              return (\n                <div\n                  key={day.toString()}\n                  className=\"border-border/70 relative border-r p-1 last:border-r-0\"\n                  data-today={isToday(day) || undefined}\n                >\n                  {dayAllDayEvents.map((event) => {\n                    const eventStart = new Date(event.start);\n                    const eventEnd = new Date(event.end);\n                    const isFirstDay = isSameDay(day, eventStart);\n                    const isLastDay = isSameDay(day, eventEnd);\n\n                    // Check if this is the first day in the current week view\n                    const isFirstVisibleDay =\n                      dayIndex === 0 && isBefore(eventStart, weekStart);\n                    const shouldShowTitle = isFirstDay || isFirstVisibleDay;\n\n                    return (\n                      <EventItem\n                        key={`spanning-${event.id}`}\n                        onClick={(e) => handleEventClick(event, e)}\n                        event={event}\n                        view=\"month\"\n                        isFirstDay={isFirstDay}\n                        isLastDay={isLastDay}\n                      >\n                        {/* Show title if it's the first day of the event or the first visible day in the week */}\n                        <div\n                          className={cn(\n                            \"truncate\",\n                            !shouldShowTitle && \"invisible\",\n                          )}\n                          aria-hidden={!shouldShowTitle}\n                        >\n                          {event.title}\n                        </div>\n                      </EventItem>\n                    );\n                  })}\n                </div>\n              );\n            })}\n          </div>\n        </div>\n      )}\n\n      <div className=\"grid flex-1 grid-cols-8 overflow-hidden\">\n        <div className=\"border-border/70 border-r grid auto-cols-fr\">\n          {hours.map((hour, index) => (\n            <div\n              key={hour.toString()}\n              className=\"border-border/70 relative min-h-[var(--week-cells-height)] border-b last:border-b-0\"\n            >\n              {index > 0 && (\n                <span className=\"bg-background text-muted-foreground/70 absolute -top-3 left-0 flex h-6 w-16 max-w-full items-center justify-end pe-2 text-[10px] sm:pe-4 sm:text-xs\">\n                  {format(hour, \"h a\")}\n                </span>\n              )}\n            </div>\n          ))}\n        </div>\n\n        {days.map((day, dayIndex) => (\n          <div\n            key={day.toString()}\n            className=\"border-border/70 relative border-r last:border-r-0 grid auto-cols-fr\"\n            data-today={isToday(day) || undefined}\n          >\n            {/* Positioned events */}\n            {(processedDayEvents[dayIndex] ?? []).map((positionedEvent) => (\n              <div\n                key={positionedEvent.event.id}\n                className=\"absolute z-10 px-0.5\"\n                style={{\n                  top: `${positionedEvent.top}px`,\n                  height: `${positionedEvent.height}px`,\n                  left: `${positionedEvent.left * 100}%`,\n                  width: `${positionedEvent.width * 100}%`,\n                  zIndex: positionedEvent.zIndex,\n                }}\n                onClick={(e) => e.stopPropagation()}\n              >\n                <div className=\"h-full w-full\">\n                  <DraggableEvent\n                    event={positionedEvent.event}\n                    view=\"week\"\n                    onClick={(e) => handleEventClick(positionedEvent.event, e)}\n                    showTime\n                    height={positionedEvent.height}\n                  />\n                </div>\n              </div>\n            ))}\n\n            {/* Current time indicator - only show for today's column */}\n            {currentTimeVisible && isToday(day) && (\n              <div\n                className=\"pointer-events-none absolute right-0 left-0 z-20\"\n                style={{ top: `${currentTimePosition}%` }}\n              >\n                <div className=\"relative flex items-center\">\n                  <div className=\"bg-red-500 absolute -left-1 h-2 w-2 rounded-full\"></div>\n                  <div className=\"bg-red-500 h-[2px] w-full\"></div>\n                </div>\n              </div>\n            )}\n            {hours.map((hour) => {\n              const hourValue = getHours(hour);\n              return (\n                <div\n                  key={hour.toString()}\n                  className=\"border-border/70 relative min-h-[var(--week-cells-height)] border-b last:border-b-0\"\n                >\n                  {/* Quarter-hour intervals */}\n                  {[0, 1, 2, 3].map((quarter) => {\n                    const quarterHourTime = hourValue + quarter * 0.25;\n                    return (\n                      <DroppableCell\n                        key={`${hour.toString()}-${quarter}`}\n                        id={`week-cell-${day.toISOString()}-${quarterHourTime}`}\n                        date={day}\n                        time={quarterHourTime}\n                        className={cn(\n                          \"absolute h-[calc(var(--week-cells-height)/4)] w-full\",\n                          quarter === 0 && \"top-0\",\n                          quarter === 1 &&\n                            \"top-[calc(var(--week-cells-height)/4)]\",\n                          quarter === 2 &&\n                            \"top-[calc(var(--week-cells-height)/4*2)]\",\n                          quarter === 3 &&\n                            \"top-[calc(var(--week-cells-height)/4*3)]\",\n                        )}\n                        onClick={() => {\n                          const startTime = new Date(day);\n                          startTime.setHours(hourValue);\n                          startTime.setMinutes(quarter * 15);\n                          onEventCreate(startTime);\n                        }}\n                      />\n                    );\n                  })}\n                </div>\n              );\n            })}\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n}\n", "type": "registry:component"}, {"path": "components/event-calendar/hooks/use-current-time-indicator.ts", "content": "\"use client\";\n\nimport { useEffect, useState } from \"react\";\nimport { endOfWeek, isSameDay, isWithinInterval, startOfWeek } from \"date-fns\";\nimport { StartHour, EndHour } from \"@/components/event-calendar/constants\";\n\nexport function useCurrentTimeIndicator(\n  currentDate: Date,\n  view: \"day\" | \"week\",\n) {\n  const [currentTimePosition, setCurrentTimePosition] = useState<number>(0);\n  const [currentTimeVisible, setCurrentTimeVisible] = useState<boolean>(false);\n\n  useEffect(() => {\n    const calculateTimePosition = () => {\n      const now = new Date();\n      const hours = now.getHours();\n      const minutes = now.getMinutes();\n      const totalMinutes = (hours - StartHour) * 60 + minutes;\n      const dayStartMinutes = 0; // 12am\n      const dayEndMinutes = (EndHour - StartHour) * 60; // 12am next day\n\n      // Calculate position as percentage of day\n      const position =\n        ((totalMinutes - dayStartMinutes) / (dayEndMinutes - dayStartMinutes)) *\n        100;\n\n      // Check if current day is in view based on the calendar view\n      let isCurrentTimeVisible = false;\n\n      if (view === \"day\") {\n        isCurrentTimeVisible = isSameDay(now, currentDate);\n      } else if (view === \"week\") {\n        const startOfWeekDate = startOfWeek(currentDate, { weekStartsOn: 0 });\n        const endOfWeekDate = endOfWeek(currentDate, { weekStartsOn: 0 });\n        isCurrentTimeVisible = isWithinInterval(now, {\n          start: startOfWeekDate,\n          end: endOfWeekDate,\n        });\n      }\n\n      setCurrentTimePosition(position);\n      setCurrentTimeVisible(isCurrentTimeVisible);\n    };\n\n    // Calculate immediately\n    calculateTimePosition();\n\n    // Update every minute\n    const interval = setInterval(calculateTimePosition, 60000);\n\n    return () => clearInterval(interval);\n  }, [currentDate, view]);\n\n  return { currentTimePosition, currentTimeVisible };\n}\n", "type": "registry:component"}, {"path": "components/event-calendar/hooks/use-event-visibility.ts", "content": "\"use client\";\n\nimport { useLayoutEffect, useMemo, useRef, useState } from \"react\";\n\ninterface EventVisibilityOptions {\n  eventHeight: number;\n  eventGap: number;\n}\n\ninterface EventVisibilityResult {\n  contentRef: React.RefObject<HTMLDivElement>;\n  contentHeight: number | null;\n  getVisibleEventCount: (totalEvents: number) => number;\n}\n\n/**\n * Hook for calculating event visibility based on container height\n * Uses ResizeObserver for efficient updates\n */\nexport function useEventVisibility({\n  eventHeight,\n  eventGap,\n}: EventVisibilityOptions): EventVisibilityResult {\n  // Use the standard pattern for React refs\n  const contentRef = useRef<HTMLDivElement>(null);\n  const observerRef = useRef<ResizeObserver | null>(null);\n  const [contentHeight, setContentHeight] = useState<number | null>(null);\n\n  // Use layout effect for synchronous measurement before paint\n  useLayoutEffect(() => {\n    if (!contentRef.current) return;\n\n    // Function to update the content height\n    const updateHeight = () => {\n      if (contentRef.current) {\n        setContentHeight(contentRef.current.clientHeight);\n      }\n    };\n\n    // Initial measurement (synchronous)\n    updateHeight();\n\n    // Create observer only once and reuse it\n    if (!observerRef.current) {\n      observerRef.current = new ResizeObserver(() => {\n        // Just call updateHeight when resize is detected\n        updateHeight();\n      });\n    }\n\n    // Start observing the content container\n    observerRef.current.observe(contentRef.current);\n\n    // Clean up function\n    return () => {\n      if (observerRef.current) {\n        observerRef.current.disconnect();\n      }\n    };\n  }, []);\n\n  // Function to calculate visible events for a cell\n  const getVisibleEventCount = useMemo(() => {\n    return (totalEvents: number): number => {\n      if (!contentHeight) return totalEvents;\n\n      // Calculate how many events can fit in the container\n      const maxEvents = Math.floor(contentHeight / (eventHeight + eventGap));\n\n      // If all events fit, show them all\n      if (totalEvents <= maxEvents) {\n        return totalEvents;\n      } else {\n        // Otherwise, reserve space for \"more\" button by showing one less\n        return maxEvents > 0 ? maxEvents - 1 : 0;\n      }\n    };\n  }, [contentHeight, eventHeight, eventGap]);\n\n  // Use type assertion to satisfy TypeScript\n  return {\n    contentRef,\n    contentHeight,\n    getVisibleEventCount,\n  } as EventVisibilityResult;\n}\n", "type": "registry:component"}, {"path": "hooks/use-mobile.ts", "content": "import * as React from \"react\";\n\nconst MOBILE_BREAKPOINT = 1024;\n\nexport function useIsMobile() {\n  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(\n    undefined,\n  );\n\n  React.useEffect(() => {\n    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`);\n    const onChange = () => {\n      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);\n    };\n    mql.addEventListener(\"change\", onChange);\n    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);\n    return () => mql.removeEventListener(\"change\", onChange);\n  }, []);\n\n  return !!isMobile;\n}\n", "type": "registry:hook"}, {"path": "providers/theme-provider.tsx", "content": "\"use client\";\n\nimport {\n  ThemeProvider as NextThemesProvider,\n  type ThemeProviderProps,\n} from \"next-themes\";\n\nexport function ThemeProvider({ children, ...props }: ThemeProviderProps) {\n  return <NextThemesProvider {...props}>{children}</NextThemesProvider>;\n}\n", "type": "registry:file", "target": "providers/theme-provider.tsx"}, {"path": "next.config.mjs", "content": "/** @type {import('next').NextConfig} */\nconst nextConfig = {\n  assetPrefix: \"/exp6-static\",\n  transpilePackages: [\"@workspace/ui\"],\n  images: {\n    remotePatterns: [\n      {\n        protocol: 'https',\n        hostname: 'raw.githubusercontent.com',\n      },\n    ],    \n  },  \n}\n\nexport default nextConfig\n", "type": "registry:file", "target": "next.config.mjs"}], "cssVars": {"theme": {"font-sans": "var(--font-sans), ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'", "font-mono": "var(--font-mono), ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace"}, "light": {"background": "oklch(0.985 0 0)", "foreground": "oklch(0.141 0.005 285.823)", "card": "oklch(0.985 0 0)", "card-foreground": "oklch(0.141 0.005 285.823)", "popover": "oklch(0.985 0 0)", "popover-foreground": "oklch(0.141 0.005 285.823)", "primary": "oklch(0.21 0.006 285.885)", "primary-foreground": "oklch(0.985 0 0)", "secondary": "oklch(0.967 0.001 286.375)", "secondary-foreground": "oklch(0.21 0.006 285.885)", "muted": "oklch(0.967 0.001 286.375)", "muted-foreground": "oklch(0.552 0.016 285.938)", "accent": "oklch(0.967 0.001 286.375)", "accent-foreground": "oklch(0.21 0.006 285.885)", "destructive": "oklch(0.577 0.245 27.325)", "destructive-foreground": "oklch(0.577 0.245 27.325)", "border": "oklch(0.92 0.004 286.32)", "input": "oklch(0.92 0.004 286.32)", "ring": "oklch(0.705 0.015 286.067)", "chart-1": "oklch(0.646 0.222 41.116)", "chart-2": "oklch(0.6 0.118 184.704)", "chart-3": "oklch(0.398 0.07 227.392)", "chart-4": "oklch(0.828 0.189 84.429)", "chart-5": "oklch(0.769 0.188 70.08)", "radius": "0.625rem", "sidebar": "oklch(0.21 0.006 285.885)", "sidebar-foreground": "oklch(0.92 0.004 286.32)", "sidebar-primary": "oklch(0.21 0.006 285.885)", "sidebar-primary-foreground": "oklch(0.21 0.006 285.885)", "sidebar-accent": "oklch(0.92 0.004 286.32)", "sidebar-accent-foreground": "oklch(0.21 0.006 285.885)", "sidebar-border": "oklch(0.92 0.004 286.32)", "sidebar-ring": "oklch(0.705 0.015 286.067)"}, "dark": {"background": "oklch(0.256 0.006 286.033)", "foreground": "oklch(0.985 0 0)", "card": "oklch(0.21 0.006 285.885)", "card-foreground": "oklch(0.985 0 0)", "popover": "oklch(0.256 0.006 286.033)", "popover-foreground": "oklch(0.985 0 0)", "primary": "oklch(0.92 0.004 286.32)", "primary-foreground": "oklch(0.21 0.006 285.885)", "secondary": "oklch(0.37 0.013 285.805)", "secondary-foreground": "oklch(0.985 0 0)", "muted": "oklch(0.37 0.013 285.805)", "muted-foreground": "oklch(0.705 0.015 286.067)", "accent": "oklch(0.37 0.013 285.805)", "accent-foreground": "oklch(0.985 0 0)", "destructive": "oklch(0.704 0.191 22.216)", "destructive-foreground": "oklch(0.704 0.191 22.216)", "border": "oklch(0.985 0 0 / 10%)", "input": "oklch(0.985 0 0 / 15%)", "ring": "oklch(0.552 0.016 285.938)", "chart-1": "oklch(0.488 0.243 264.376)", "chart-2": "oklch(0.696 0.17 162.48)", "chart-3": "oklch(0.769 0.188 70.08)", "chart-4": "oklch(0.627 0.265 303.9)", "chart-5": "oklch(0.645 0.246 16.439)", "radius": "0.625rem", "sidebar": "oklch(0.21 0.006 285.885)", "sidebar-foreground": "oklch(0.967 0.001 286.375)", "sidebar-primary": "oklch(0.488 0.243 264.376)", "sidebar-primary-foreground": "oklch(0.985 0 0)", "sidebar-accent": "oklch(0.256 0.006 286.033)", "sidebar-accent-foreground": "oklch(0.985 0 0)", "sidebar-border": "oklch(0.985 0 0 / 10%)", "sidebar-ring": "oklch(0.442 0.017 285.786)"}}}