# Phase 4: Session Confirmation & Credit Deduction

## 4.1 Session Status Workflow

- Add session status (scheduled → confirmed → completed)
- Create admin approval queue interface
- Implement status change notifications

## 4.2 Credit Deduction System

- Auto-deduct credits when sessions confirmed
- Add transaction logging for credit changes
- Implement rollback functionality for cancellations

## 4.3 Validation & Business Rules

- Enforce min/max participant limits
- Prevent zero-credit customer scheduling
- Add session capacity warnings
- Implement trainer schedule conflict detection

---

### Tasks

#### Status Workflow

- Implement session status state machine
- Create admin approval interface placeholder
- Add status change notifications
- Build session status tracking

#### Credit System

- Implement automatic credit deduction
- Add transaction logging
- Create credit balance validation
- Add rollback functionality for cancellations

#### Business Rules & Validation

- Enforce participant limits (3-5 per session)
- Prevent zero-credit customer scheduling
- Add comprehensive validation
- Implement conflict detection
