import { betterAuth, BetterAuthOptions } from 'better-auth';
import { nextCookies } from 'better-auth/next-js';
import { drizzleAdapter } from 'better-auth/adapters/drizzle';
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as schema from './schema';

// Database connection
const connectionString = process.env.DATABASE_URL || 'postgresql://localhost:5432/loolookids';
const sql = postgres(connectionString);
const db = drizzle(sql, { schema });

const options = {
  database: drizzleAdapter(db, {
    provider: 'pg',
    // schema: {
    //   user: schema.user,
    //   session: schema.session,
    //   account: schema.account,
    //   verification: schema.verification,
    // },
  }),
  emailAndPassword: {
    enabled: true,
  },
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24, // 1 day
  },
  user: {
    additionalFields: {
      role: {
        type: 'string',
        defaultValue: 'trainer',
      },
    },
  },
  plugins: [nextCookies()],
} satisfies BetterAuthOptions;

export const auth: ReturnType<typeof betterAuth<typeof options>> = betterAuth(options);

export type Session = typeof auth.$Infer.Session;
export type User = typeof auth.$Infer.Session.user;

export { db };
