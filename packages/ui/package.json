{"name": "@workspace/ui", "version": "0.0.0", "type": "module", "private": true, "scripts": {"lint": "eslint . --max-warnings 0"}, "dependencies": {"@radix-ui/react-slot": "^1.1.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "0.475.0", "next-themes": "^0.4.4", "react": "^19.1.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.69"}, "devDependencies": {"@turbo/gen": "^2.4.2", "@types/node": "^22.14.0", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.1", "@workspace/eslint-config": "workspace:*", "@workspace/typescript-config": "workspace:*", "autoprefixer": "^10.4.20", "postcss": "^8.5.3", "react": "^19.1.0", "tailwindcss": "^4.1.1", "typescript": "^5.8.2"}, "exports": {"./globals.css": "./src/styles/globals.css", "./postcss.config": "./postcss.config.mjs", "./lib/*": "./src/lib/*.ts", "./components/*": "./src/components/*.tsx", "./hooks/*": "./src/hooks/*.ts"}}